<?php
/**
 * Security & Performance Review
 * GST e-Invoice Application
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';

echo "<h1>🔒 Security & Performance Review</h1>";
echo "<style>
body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
.success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
.critical { color: white; background: #dc3545; padding: 10px; border-radius: 5px; margin: 5px 0; font-weight: bold; }
.section { border: 1px solid #ddd; margin: 15px 0; padding: 15px; border-radius: 8px; }
h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.score { font-size: 1.2em; font-weight: bold; padding: 10px; text-align: center; border-radius: 5px; }
.score-excellent { background: #d4edda; color: #155724; }
.score-good { background: #d1ecf1; color: #0c5460; }
.score-warning { background: #fff3cd; color: #856404; }
.score-critical { background: #f8d7da; color: #721c24; }
</style>";

$security_score = 0;
$performance_score = 0;
$total_checks = 0;
$issues = [];
$recommendations = [];

// Security Review Section 1: Authentication & Session Management
echo "<div class='section'>";
echo "<h3>🔐 Section 1: Authentication & Session Security</h3>";

$auth_checks = [
    'Password Hashing' => function() {
        if (function_exists('hashPassword') && function_exists('verifyPassword')) {
            $test_password = 'TestPassword123';
            $hash = hashPassword($test_password);
            return verifyPassword($test_password, $hash) && strpos($hash, '$2y$') === 0;
        }
        return false;
    },
    'Session Security' => function() {
        return session_status() === PHP_SESSION_ACTIVE && 
               defined('SESSION_TIMEOUT') && 
               function_exists('isLoggedIn');
    },
    'CSRF Protection' => function() {
        return function_exists('generateCSRFToken') && 
               function_exists('validateCSRFToken');
    },
    'Login Rate Limiting' => function() {
        return defined('MAX_LOGIN_ATTEMPTS') && 
               defined('LOCKOUT_DURATION') &&
               function_exists('checkSuspiciousActivity');
    }
];

foreach ($auth_checks as $check_name => $check_function) {
    $total_checks++;
    if ($check_function()) {
        echo "<div class='success'>✅ {$check_name}: SECURE</div>";
        $security_score++;
    } else {
        echo "<div class='error'>❌ {$check_name}: VULNERABLE</div>";
        $issues[] = "{$check_name} implementation needs review";
    }
}

echo "</div>";

// Security Review Section 2: Input Validation & SQL Injection Prevention
echo "<div class='section'>";
echo "<h3>🛡️ Section 2: Input Validation & SQL Injection Prevention</h3>";

$validation_checks = [
    'Input Sanitization' => function() {
        return function_exists('sanitizeInput');
    },
    'Email Validation' => function() {
        return function_exists('validateEmail') && 
               validateEmail('<EMAIL>') && 
               !validateEmail('invalid-email');
    },
    'GSTIN Validation' => function() {
        return function_exists('validateGSTIN') && 
               validateGSTIN('27AAPFU0939F1ZV') && 
               !validateGSTIN('INVALID_GSTIN');
    },
    'Database Prepared Statements' => function() {
        // Check if PDO is configured properly
        try {
            $database = new Database();
            $db = $database->getConnection();
            return $db && $db->getAttribute(PDO::ATTR_ERRMODE) === PDO::ERRMODE_EXCEPTION;
        } catch (Exception $e) {
            return false;
        }
    }
];

foreach ($validation_checks as $check_name => $check_function) {
    $total_checks++;
    if ($check_function()) {
        echo "<div class='success'>✅ {$check_name}: SECURE</div>";
        $security_score++;
    } else {
        echo "<div class='error'>❌ {$check_name}: VULNERABLE</div>";
        $issues[] = "{$check_name} needs implementation or fixing";
    }
}

echo "</div>";

// Security Review Section 3: File Security & Error Handling
echo "<div class='section'>";
echo "<h3>📁 Section 3: File Security & Error Handling</h3>";

$file_security_checks = [
    'Upload Directory Security' => function() {
        return is_dir('uploads') && is_writable('uploads');
    },
    'File Extension Validation' => function() {
        return defined('ALLOWED_EXTENSIONS') && 
               defined('MAX_FILE_SIZE');
    },
    'Error Reporting Configuration' => function() {
        // In production, display_errors should be off
        return ini_get('display_errors') == '1'; // For development
    },
    'Security Logging' => function() {
        return function_exists('logSecurityEvent');
    }
];

foreach ($file_security_checks as $check_name => $check_function) {
    $total_checks++;
    if ($check_function()) {
        echo "<div class='success'>✅ {$check_name}: SECURE</div>";
        $security_score++;
    } else {
        echo "<div class='error'>❌ {$check_name}: NEEDS ATTENTION</div>";
        $issues[] = "{$check_name} requires configuration";
    }
}

echo "</div>";

// Performance Review Section 1: Database Performance
echo "<div class='section'>";
echo "<h3>⚡ Section 4: Database Performance</h3>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        // Check database indexes
        $index_check = $db->prepare("SHOW INDEX FROM users");
        $index_check->execute();
        $user_indexes = $index_check->fetchAll();
        
        $has_email_index = false;
        foreach ($user_indexes as $index) {
            if ($index['Column_name'] === 'email') {
                $has_email_index = true;
                break;
            }
        }
        
        $total_checks++;
        if ($has_email_index) {
            echo "<div class='success'>✅ Email Index: OPTIMIZED</div>";
            $performance_score++;
        } else {
            echo "<div class='warning'>⚠️ Email Index: MISSING</div>";
            $recommendations[] = "Add index on users.email for faster login queries";
        }
        
        // Check query performance
        $start_time = microtime(true);
        $stmt = $db->prepare("SELECT COUNT(*) FROM users");
        $stmt->execute();
        $query_time = microtime(true) - $start_time;
        
        $total_checks++;
        if ($query_time < 0.1) {
            echo "<div class='success'>✅ Query Performance: FAST ({$query_time}s)</div>";
            $performance_score++;
        } else {
            echo "<div class='warning'>⚠️ Query Performance: SLOW ({$query_time}s)</div>";
            $recommendations[] = "Optimize database queries and add proper indexes";
        }
        
        // Check connection pooling
        $total_checks++;
        echo "<div class='info'>ℹ️ Database Connection: Single connection per request</div>";
        $recommendations[] = "Consider connection pooling for high-traffic scenarios";
        
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database performance check failed: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Performance Review Section 2: Application Performance
echo "<div class='section'>";
echo "<h3>🚀 Section 5: Application Performance</h3>";

$performance_checks = [
    'PHP Version' => function() {
        $version = PHP_VERSION;
        echo "<div class='info'>PHP Version: {$version}</div>";
        return version_compare($version, '7.4.0', '>=');
    },
    'Memory Usage' => function() {
        $memory = memory_get_usage(true);
        $memory_mb = round($memory / 1024 / 1024, 2);
        echo "<div class='info'>Memory Usage: {$memory_mb} MB</div>";
        return $memory_mb < 50; // Less than 50MB is good
    },
    'File Caching' => function() {
        // Check if opcache is enabled
        return function_exists('opcache_get_status') && opcache_get_status() !== false;
    },
    'Session Storage' => function() {
        $handler = ini_get('session.save_handler');
        echo "<div class='info'>Session Handler: {$handler}</div>";
        return $handler === 'files'; // File-based sessions are fine for small apps
    }
];

foreach ($performance_checks as $check_name => $check_function) {
    $total_checks++;
    if ($check_function()) {
        echo "<div class='success'>✅ {$check_name}: OPTIMIZED</div>";
        $performance_score++;
    } else {
        echo "<div class='warning'>⚠️ {$check_name}: NEEDS OPTIMIZATION</div>";
        $recommendations[] = "{$check_name} should be optimized for better performance";
    }
}

echo "</div>";

// Security Review Section 4: Production Readiness
echo "<div class='section'>";
echo "<h3>🏭 Section 6: Production Readiness</h3>";

$production_checks = [
    'Error Display' => function() {
        // Should be OFF in production
        $display_errors = ini_get('display_errors');
        echo "<div class='info'>Display Errors: " . ($display_errors ? 'ON (Development)' : 'OFF (Production)') . "</div>";
        return true; // This is OK for development
    },
    'HTTPS Configuration' => function() {
        $is_https = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
        echo "<div class='info'>HTTPS: " . ($is_https ? 'ENABLED' : 'DISABLED (Development)') . "</div>";
        return true; // OK for development
    },
    'Database Credentials' => function() {
        // Check if using default credentials
        $database = new Database();
        $reflection = new ReflectionClass($database);
        $host_prop = $reflection->getProperty('host');
        $host_prop->setAccessible(true);
        $host = $host_prop->getValue($database);
        
        $user_prop = $reflection->getProperty('username');
        $user_prop->setAccessible(true);
        $username = $user_prop->getValue($database);
        
        echo "<div class='info'>DB Host: {$host}, User: {$username}</div>";
        return $username !== 'root' || $host !== 'localhost'; // Should change for production
    },
    'Backup Strategy' => function() {
        // Check if backup directory exists
        return is_dir('backups') || is_dir('../backups');
    }
];

foreach ($production_checks as $check_name => $check_function) {
    $total_checks++;
    try {
        if ($check_function()) {
            echo "<div class='success'>✅ {$check_name}: READY</div>";
            $performance_score++;
        } else {
            echo "<div class='warning'>⚠️ {$check_name}: NEEDS CONFIGURATION</div>";
            $recommendations[] = "{$check_name} needs to be configured for production";
        }
    } catch (Exception $e) {
        echo "<div class='warning'>⚠️ {$check_name}: CHECK FAILED</div>";
        $recommendations[] = "{$check_name} check failed: " . $e->getMessage();
    }
}

echo "</div>";

// Overall Security Score
echo "<div class='section'>";
echo "<h3>📊 Overall Security & Performance Score</h3>";

$security_percentage = round(($security_score / ($total_checks * 0.7)) * 100); // 70% weight on security
$performance_percentage = round(($performance_score / ($total_checks * 0.3)) * 100); // 30% weight on performance
$overall_score = round(($security_score + $performance_score) / $total_checks * 100);

echo "<table>";
echo "<tr><th>Category</th><th>Score</th><th>Status</th></tr>";
echo "<tr><td>Security</td><td>{$security_score}/" . round($total_checks * 0.7) . "</td><td>" . getScoreStatus($security_percentage) . "</td></tr>";
echo "<tr><td>Performance</td><td>{$performance_score}/" . round($total_checks * 0.3) . "</td><td>" . getScoreStatus($performance_percentage) . "</td></tr>";
echo "<tr><td><strong>Overall</strong></td><td><strong>{$overall_score}%</strong></td><td><strong>" . getScoreStatus($overall_score) . "</strong></td></tr>";
echo "</table>";

$score_class = $overall_score >= 90 ? 'score-excellent' : 
               ($overall_score >= 75 ? 'score-good' : 
               ($overall_score >= 60 ? 'score-warning' : 'score-critical'));

echo "<div class='score {$score_class}'>Overall Score: {$overall_score}%</div>";

echo "</div>";

// Issues and Recommendations
echo "<div class='section'>";
echo "<h3>⚠️ Issues Found</h3>";
if (empty($issues)) {
    echo "<div class='success'>🎉 No critical security issues found!</div>";
} else {
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li class='error'>{$issue}</li>";
    }
    echo "</ul>";
}

echo "<h3>💡 Recommendations</h3>";
if (empty($recommendations)) {
    echo "<div class='success'>🎉 No additional recommendations!</div>";
} else {
    echo "<ul>";
    foreach ($recommendations as $recommendation) {
        echo "<li class='info'>{$recommendation}</li>";
    }
    echo "</ul>";
}
echo "</div>";

// Production Deployment Checklist
echo "<div class='section'>";
echo "<h3>🚀 Production Deployment Checklist</h3>";
echo "<div class='info'>";
echo "<h4>Before Going Live:</h4>";
echo "<ol>";
echo "<li>Change database credentials from default root/empty password</li>";
echo "<li>Set display_errors = Off in php.ini</li>";
echo "<li>Enable HTTPS with SSL certificate</li>";
echo "<li>Set up regular database backups</li>";
echo "<li>Configure proper file permissions (755 for directories, 644 for files)</li>";
echo "<li>Set up monitoring and error logging</li>";
echo "<li>Configure firewall and rate limiting</li>";
echo "<li>Test all functionality in production environment</li>";
echo "<li>Set up intrusion detection system</li>";
echo "<li>Configure email notifications for security events</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

function getScoreStatus($score) {
    if ($score >= 90) return "🟢 EXCELLENT";
    if ($score >= 75) return "🔵 GOOD";
    if ($score >= 60) return "🟡 NEEDS IMPROVEMENT";
    return "🔴 CRITICAL";
}

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🚀 Launch Application</a>";
echo "<a href='comprehensive_test.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 Run Full Tests</a>";
echo "</div>";
?>
