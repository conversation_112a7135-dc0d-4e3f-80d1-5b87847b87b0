<?php
/**
 * Login Page
 * GST e-Invoice Application
 */

require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Validate CSRF token
    if (!validateCSRFToken($csrf_token)) {
        $error_message = 'Invalid request. Please try again.';
    } else {
        // Validate inputs
        if (empty($email) || empty($password)) {
            $error_message = 'Please enter both email and password.';
        } else {
            try {
                $database = new Database();
                $db = $database->getConnection();
                
                // Check if user exists and is not locked
                $stmt = $db->prepare("
                    SELECT id, name, email, password, role, failed_attempts, locked_until 
                    FROM users 
                    WHERE email = ? AND is_active = 1
                ");
                $stmt->execute([$email]);
                $user = $stmt->fetch();
                
                if ($user) {
                    // Check if account is locked
                    if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                        $remaining = ceil((strtotime($user['locked_until']) - time()) / 60);
                        $error_message = "Account is locked. Try again in $remaining minutes.";
                    } else {
                        // Verify password
                        if (password_verify($password, $user['password'])) {
                            // Reset failed attempts and unlock account
                            $stmt = $db->prepare("
                                UPDATE users 
                                SET failed_attempts = 0, locked_until = NULL, last_login = NOW() 
                                WHERE id = ?
                            ");
                            $stmt->execute([$user['id']]);
                            
                            // Set session variables
                            $_SESSION['user_id'] = $user['id'];
                            $_SESSION['user_name'] = $user['name'];
                            $_SESSION['user_email'] = $user['email'];
                            $_SESSION['user_role'] = $user['role'];
                            $_SESSION['login_time'] = time();
                            
                            // Log successful login
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                VALUES (?, 'users', ?, 'LOGIN', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $user['id'], 
                                $user['id'], 
                                json_encode(['login_time' => date('Y-m-d H:i:s')]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                            
                            // Redirect to dashboard
                            header('Location: dashboard.php');
                            exit();
                        } else {
                            // Increment failed attempts
                            $failed_attempts = $user['failed_attempts'] + 1;
                            $locked_until = null;
                            
                            if ($failed_attempts >= MAX_LOGIN_ATTEMPTS) {
                                $locked_until = date('Y-m-d H:i:s', time() + LOCKOUT_DURATION);
                                $error_message = 'Too many failed attempts. Account locked for ' . (LOCKOUT_DURATION / 60) . ' minutes.';
                            } else {
                                $remaining_attempts = MAX_LOGIN_ATTEMPTS - $failed_attempts;
                                $error_message = "Invalid credentials. $remaining_attempts attempts remaining.";
                            }
                            
                            $stmt = $db->prepare("
                                UPDATE users 
                                SET failed_attempts = ?, locked_until = ? 
                                WHERE id = ?
                            ");
                            $stmt->execute([$failed_attempts, $locked_until, $user['id']]);
                        }
                    }
                } else {
                    $error_message = 'Invalid email or password.';
                }
            } catch (Exception $e) {
                $error_message = 'Login failed. Please try again.';
                error_log('Login error: ' . $e->getMessage());
            }
        }
    }
}

$page_title = 'Login';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-file-invoice-dollar fa-3x mb-3"></i>
                <h3><?php echo APP_NAME; ?></h3>
                <p class="mb-0">GST e-Invoice Preparing & Printing Tool</p>
            </div>
            
            <div class="login-body">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>
                            Email Address
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                               required autocomplete="email">
                        <div class="invalid-feedback">
                            Please enter a valid email address.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>
                            Password
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" 
                                   required autocomplete="current-password">
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            Please enter your password.
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            Remember me
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Login
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <a href="modules/auth/forgot-password.php" class="text-decoration-none">
                        <i class="fas fa-key me-1"></i>
                        Forgot Password?
                    </a>
                </div>
                
                <div class="mt-4 text-center text-muted">
                    <small>
                        Demo Credentials:<br>
                        Admin: <EMAIL> / Admin@123<br>
                        User: <EMAIL> / Test@123
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    </script>
</body>
</html>
