<?php
/**
 * Recipient Master Management
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login
requireLogin();

$page_title = 'Recipient Master';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!validateCSRFToken($csrf_token)) {
            $_SESSION['error_message'] = 'Invalid request. Please try again.';
        } else {
            switch ($action) {
                case 'create':
                    $name = sanitizeInput($_POST['name'] ?? '');
                    $address = sanitizeInput($_POST['address'] ?? '');
                    $state = sanitizeInput($_POST['state'] ?? '');
                    $gstin = strtoupper(sanitizeInput($_POST['gstin'] ?? ''));
                    $contact_person = sanitizeInput($_POST['contact_person'] ?? '');
                    $email = sanitizeInput($_POST['email'] ?? '');
                    $phone = sanitizeInput($_POST['phone'] ?? '');
                    $pincode = sanitizeInput($_POST['pincode'] ?? '');
                    
                    if (empty($name) || empty($state)) {
                        $_SESSION['error_message'] = 'Name and state are required.';
                    } elseif (!empty($gstin) && !preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $gstin)) {
                        $_SESSION['error_message'] = 'Invalid GSTIN format.';
                    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $_SESSION['error_message'] = 'Invalid email address.';
                    } elseif (!empty($pincode) && !preg_match('/^[0-9]{6}$/', $pincode)) {
                        $_SESSION['error_message'] = 'Invalid pincode format.';
                    } else {
                        // Check if GSTIN already exists
                        if (!empty($gstin)) {
                            $stmt = $db->prepare("SELECT id FROM recipients WHERE gstin = ?");
                            $stmt->execute([$gstin]);
                            
                            if ($stmt->fetch()) {
                                $_SESSION['error_message'] = 'GSTIN already exists.';
                                break;
                            }
                        }
                        
                        $stmt = $db->prepare("
                            INSERT INTO recipients (name, address, state, gstin, contact_person, email, phone, pincode, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        
                        if ($stmt->execute([$name, $address, $state, $gstin, $contact_person, $email, $phone, $pincode, $_SESSION['user_id']])) {
                            $_SESSION['success_message'] = 'Recipient created successfully.';
                            
                            // Log recipient creation
                            $recipient_id = $db->lastInsertId();
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                VALUES (?, 'recipients', ?, 'INSERT', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $recipient_id, 
                                json_encode(['name' => $name, 'gstin' => $gstin]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to create recipient.';
                        }
                    }
                    break;
                    
                case 'update':
                    $recipient_id = (int)($_POST['recipient_id'] ?? 0);
                    $name = sanitizeInput($_POST['name'] ?? '');
                    $address = sanitizeInput($_POST['address'] ?? '');
                    $state = sanitizeInput($_POST['state'] ?? '');
                    $gstin = strtoupper(sanitizeInput($_POST['gstin'] ?? ''));
                    $contact_person = sanitizeInput($_POST['contact_person'] ?? '');
                    $email = sanitizeInput($_POST['email'] ?? '');
                    $phone = sanitizeInput($_POST['phone'] ?? '');
                    $pincode = sanitizeInput($_POST['pincode'] ?? '');
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    if (empty($name) || empty($state)) {
                        $_SESSION['error_message'] = 'Name and state are required.';
                    } elseif (!empty($gstin) && !preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $gstin)) {
                        $_SESSION['error_message'] = 'Invalid GSTIN format.';
                    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $_SESSION['error_message'] = 'Invalid email address.';
                    } elseif (!empty($pincode) && !preg_match('/^[0-9]{6}$/', $pincode)) {
                        $_SESSION['error_message'] = 'Invalid pincode format.';
                    } else {
                        // Check if GSTIN exists for other recipients
                        if (!empty($gstin)) {
                            $stmt = $db->prepare("SELECT id FROM recipients WHERE gstin = ? AND id != ?");
                            $stmt->execute([$gstin, $recipient_id]);
                            
                            if ($stmt->fetch()) {
                                $_SESSION['error_message'] = 'GSTIN already exists.';
                                break;
                            }
                        }
                        
                        $stmt = $db->prepare("
                            UPDATE recipients 
                            SET name = ?, address = ?, state = ?, gstin = ?, contact_person = ?, email = ?, phone = ?, pincode = ?, is_active = ? 
                            WHERE id = ?
                        ");
                        
                        if ($stmt->execute([$name, $address, $state, $gstin, $contact_person, $email, $phone, $pincode, $is_active, $recipient_id])) {
                            $_SESSION['success_message'] = 'Recipient updated successfully.';
                            
                            // Log recipient update
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                VALUES (?, 'recipients', ?, 'UPDATE', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $recipient_id, 
                                json_encode(['name' => $name, 'gstin' => $gstin, 'is_active' => $is_active]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to update recipient.';
                        }
                    }
                    break;
                    
                case 'delete':
                    $recipient_id = (int)($_POST['recipient_id'] ?? 0);
                    
                    // Check if recipient has invoices
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoices WHERE recipient_id = ?");
                    $stmt->execute([$recipient_id]);
                    $invoice_count = $stmt->fetch()['count'];
                    
                    if ($invoice_count > 0) {
                        $_SESSION['error_message'] = 'Cannot delete recipient with existing invoices. Deactivate instead.';
                    } else {
                        $stmt = $db->prepare("DELETE FROM recipients WHERE id = ?");
                        
                        if ($stmt->execute([$recipient_id])) {
                            $_SESSION['success_message'] = 'Recipient deleted successfully.';
                            
                            // Log recipient deletion
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, old_values, ip_address, user_agent) 
                                VALUES (?, 'recipients', ?, 'DELETE', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $recipient_id, 
                                json_encode(['deleted_by' => $_SESSION['user_name']]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to delete recipient.';
                        }
                    }
                    break;
            }
        }
        
        header('Location: recipients.php');
        exit();
    }
    
    // Handle CSV import
    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
        $csv_file = $_FILES['csv_file']['tmp_name'];
        $imported = 0;
        $errors = [];
        
        if (($handle = fopen($csv_file, 'r')) !== FALSE) {
            $header = fgetcsv($handle); // Skip header row
            
            while (($data = fgetcsv($handle)) !== FALSE) {
                if (count($data) >= 4) { // Minimum required fields
                    $name = sanitizeInput($data[0] ?? '');
                    $address = sanitizeInput($data[1] ?? '');
                    $state = sanitizeInput($data[2] ?? '');
                    $gstin = strtoupper(sanitizeInput($data[3] ?? ''));
                    $contact_person = sanitizeInput($data[4] ?? '');
                    $email = sanitizeInput($data[5] ?? '');
                    $phone = sanitizeInput($data[6] ?? '');
                    $pincode = sanitizeInput($data[7] ?? '');
                    
                    if (!empty($name) && !empty($state)) {
                        // Validate GSTIN if provided
                        if (!empty($gstin) && !preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $gstin)) {
                            $errors[] = "Invalid GSTIN for $name: $gstin";
                            continue;
                        }
                        
                        // Check if GSTIN already exists
                        if (!empty($gstin)) {
                            $stmt = $db->prepare("SELECT id FROM recipients WHERE gstin = ?");
                            $stmt->execute([$gstin]);
                            
                            if ($stmt->fetch()) {
                                $errors[] = "GSTIN already exists for $name: $gstin";
                                continue;
                            }
                        }
                        
                        $stmt = $db->prepare("
                            INSERT INTO recipients (name, address, state, gstin, contact_person, email, phone, pincode, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        
                        if ($stmt->execute([$name, $address, $state, $gstin, $contact_person, $email, $phone, $pincode, $_SESSION['user_id']])) {
                            $imported++;
                        } else {
                            $errors[] = "Failed to import $name";
                        }
                    }
                }
            }
            fclose($handle);
        }
        
        if ($imported > 0) {
            $_SESSION['success_message'] = "$imported recipients imported successfully.";
        }
        
        if (!empty($errors)) {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
        
        header('Location: recipients.php');
        exit();
    }
    
    // Get all recipients with filtering
    $search = $_GET['search'] ?? '';
    $state_filter = $_GET['state'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(r.name LIKE ? OR r.gstin LIKE ? OR r.contact_person LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($state_filter)) {
        $where_conditions[] = "r.state = ?";
        $params[] = $state_filter;
    }
    
    if ($status_filter !== '') {
        $where_conditions[] = "r.is_active = ?";
        $params[] = (int)$status_filter;
    }
    
    // Add user restriction for non-admin users
    if (!isAdmin()) {
        $where_conditions[] = "r.created_by = ?";
        $params[] = $_SESSION['user_id'];
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $stmt = $db->prepare("
        SELECT r.*, 
               u.name as created_by_name,
               COUNT(i.id) as invoice_count
        FROM recipients r
        LEFT JOIN users u ON r.created_by = u.id
        LEFT JOIN invoices i ON r.id = i.recipient_id
        $where_clause
        GROUP BY r.id
        ORDER BY r.created_at DESC
    ");
    $stmt->execute($params);
    $recipients = $stmt->fetchAll();
    
    // Get unique states for filter
    $stmt = $db->prepare("SELECT DISTINCT state FROM recipients WHERE state IS NOT NULL AND state != '' ORDER BY state");
    $stmt->execute();
    $states = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch (Exception $e) {
    error_log('Recipient management error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while loading recipients.';
    $recipients = [];
    $states = [];
}

include '../../includes/header.php';
?>
