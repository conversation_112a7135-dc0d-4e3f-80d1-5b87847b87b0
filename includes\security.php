<?php
/**
 * Security Functions
 * GST e-Invoice Application
 */

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email format
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Indian format)
 */
function validatePhone($phone) {
    // Remove all non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Check if it's a valid Indian mobile number
    return preg_match('/^[6-9][0-9]{9}$/', $phone) || 
           preg_match('/^[0-9]{10,11}$/', $phone);
}

/**
 * Validate GSTIN format
 */
function validateGSTIN($gstin) {
    // Remove spaces and convert to uppercase
    $gstin = strtoupper(str_replace(' ', '', $gstin));
    
    // Check GSTIN format: 15 characters
    // Format: 2 digits (state code) + 10 characters (PAN) + 1 character (entity number) + 1 character (Z) + 1 character (checksum)
    return preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $gstin);
}

/**
 * Validate PAN format
 */
function validatePAN($pan) {
    $pan = strtoupper(str_replace(' ', '', $pan));
    return preg_match('/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $pan);
}

/**
 * Validate HSN/SAC code
 */
function validateHSNSAC($code) {
    // HSN/SAC codes are 4-8 digit numbers
    return preg_match('/^[0-9]{4,8}$/', $code);
}

/**
 * Validate pincode (Indian)
 */
function validatePincode($pincode) {
    return preg_match('/^[0-9]{6}$/', $pincode);
}

/**
 * Validate invoice number format
 */
function validateInvoiceNumber($invoice_number) {
    // Allow alphanumeric characters, hyphens, and forward slashes
    return preg_match('/^[A-Z0-9\-\/]{1,50}$/i', $invoice_number);
}

/**
 * Validate amount (positive number with up to 2 decimal places)
 */
function validateAmount($amount) {
    return is_numeric($amount) && $amount >= 0 && preg_match('/^\d+(\.\d{1,2})?$/', $amount);
}

/**
 * Validate quantity (positive number with up to 3 decimal places)
 */
function validateQuantity($quantity) {
    return is_numeric($quantity) && $quantity > 0 && preg_match('/^\d+(\.\d{1,3})?$/', $quantity);
}

/**
 * Validate GST rate
 */
function validateGSTRate($rate) {
    $valid_rates = [0, 0.25, 3, 5, 12, 18, 28];
    return in_array((float)$rate, $valid_rates);
}

/**
 * Validate date format (YYYY-MM-DD)
 */
function validateDate($date) {
    $d = DateTime::createFromFormat('Y-m-d', $date);
    return $d && $d->format('Y-m-d') === $date;
}

/**
 * Check password strength
 */
function validatePasswordStrength($password) {
    $errors = [];
    
    if (strlen($password) < 8) {
        $errors[] = 'Password must be at least 8 characters long';
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = 'Password must contain at least one uppercase letter';
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = 'Password must contain at least one lowercase letter';
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = 'Password must contain at least one number';
    }
    
    if (!preg_match('/[^A-Za-z0-9]/', $password)) {
        $errors[] = 'Password must contain at least one special character';
    }
    
    return $errors;
}

/**
 * Rate limiting check
 */
function checkRateLimit($action, $identifier, $max_attempts = 5, $time_window = 300) {
    $key = "rate_limit_{$action}_{$identifier}";
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = [
            'attempts' => 0,
            'first_attempt' => time()
        ];
    }
    
    $rate_data = $_SESSION[$key];
    
    // Reset if time window has passed
    if (time() - $rate_data['first_attempt'] > $time_window) {
        $_SESSION[$key] = [
            'attempts' => 1,
            'first_attempt' => time()
        ];
        return true;
    }
    
    // Check if limit exceeded
    if ($rate_data['attempts'] >= $max_attempts) {
        return false;
    }
    
    // Increment attempts
    $_SESSION[$key]['attempts']++;
    return true;
}

/**
 * Get remaining rate limit time
 */
function getRateLimitTime($action, $identifier, $time_window = 300) {
    $key = "rate_limit_{$action}_{$identifier}";
    
    if (!isset($_SESSION[$key])) {
        return 0;
    }
    
    $rate_data = $_SESSION[$key];
    $elapsed = time() - $rate_data['first_attempt'];
    
    return max(0, $time_window - $elapsed);
}

/**
 * Log security event
 */
function logSecurityEvent($event_type, $description, $user_id = null, $ip_address = null) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $ip_address = $ip_address ?: $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt = $db->prepare("
            INSERT INTO security_log (event_type, description, user_id, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([$event_type, $description, $user_id, $ip_address, $user_agent]);
        
    } catch (Exception $e) {
        error_log('Security logging error: ' . $e->getMessage());
    }
}

/**
 * Check for suspicious activity
 */
function checkSuspiciousActivity($user_id = null) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        // Check for multiple failed login attempts from same IP
        $stmt = $db->prepare("
            SELECT COUNT(*) as failed_attempts
            FROM security_log 
            WHERE event_type = 'failed_login' 
            AND ip_address = ? 
            AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$ip_address]);
        $result = $stmt->fetch();
        
        if ($result['failed_attempts'] > 10) {
            logSecurityEvent('suspicious_activity', "Multiple failed login attempts from IP: {$ip_address}", $user_id, $ip_address);
            return true;
        }
        
        // Check for rapid requests from same IP
        $stmt = $db->prepare("
            SELECT COUNT(*) as request_count
            FROM security_log 
            WHERE ip_address = ? 
            AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ");
        $stmt->execute([$ip_address]);
        $result = $stmt->fetch();
        
        if ($result['request_count'] > 100) {
            logSecurityEvent('suspicious_activity', "Rapid requests from IP: {$ip_address}", $user_id, $ip_address);
            return true;
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log('Suspicious activity check error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Generate secure random token
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Hash password securely
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Check if password needs rehashing
 */
function needsRehash($hash) {
    return password_needs_rehash($hash, PASSWORD_DEFAULT);
}

/**
 * Validate file upload
 */
function validateFileUpload($file, $allowed_types = ['csv'], $max_size = 2097152) { // 2MB default
    $errors = [];
    
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        $errors[] = 'No file uploaded or upload error occurred';
        return $errors;
    }
    
    // Check file size
    if ($file['size'] > $max_size) {
        $errors[] = 'File size exceeds maximum allowed size (' . formatBytes($max_size) . ')';
    }
    
    // Check file type
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        $errors[] = 'File type not allowed. Allowed types: ' . implode(', ', $allowed_types);
    }
    
    // Check MIME type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    $allowed_mimes = [
        'csv' => 'text/csv',
        'txt' => 'text/plain'
    ];
    
    $expected_mime = $allowed_mimes[$file_extension] ?? null;
    if ($expected_mime && $mime_type !== $expected_mime && $mime_type !== 'text/plain') {
        $errors[] = 'File content does not match file extension';
    }
    
    return $errors;
}

/**
 * Format bytes for display
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Clean filename for safe storage
 */
function cleanFilename($filename) {
    // Remove any path information
    $filename = basename($filename);
    
    // Remove special characters except dots, hyphens, and underscores
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
    
    // Remove multiple consecutive dots or underscores
    $filename = preg_replace('/[._-]+/', '_', $filename);
    
    // Ensure filename is not too long
    if (strlen($filename) > 100) {
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $name = pathinfo($filename, PATHINFO_FILENAME);
        $filename = substr($name, 0, 96 - strlen($extension)) . '.' . $extension;
    }
    
    return $filename;
}
?>
