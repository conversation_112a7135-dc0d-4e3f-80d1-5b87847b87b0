<?php
/**
 * Forgot Password Page
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ../../dashboard.php');
    exit();
}

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Validate CSRF token
    if (!validateCSRFToken($csrf_token)) {
        $message = 'Invalid request. Please try again.';
        $message_type = 'danger';
    } else {
        // Validate email
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address.';
            $message_type = 'danger';
        } else {
            try {
                $database = new Database();
                $db = $database->getConnection();
                
                // Check if user exists
                $stmt = $db->prepare("SELECT id, name FROM users WHERE email = ? AND is_active = 1");
                $stmt->execute([$email]);
                $user = $stmt->fetch();
                
                if ($user) {
                    // Generate reset token
                    $reset_token = bin2hex(random_bytes(32));
                    $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
                    
                    // Save reset token
                    $stmt = $db->prepare("
                        UPDATE users 
                        SET reset_token = ?, reset_token_expires = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$reset_token, $expires, $user['id']]);
                    
                    // Create reset link
                    $reset_link = BASE_URL . "modules/auth/reset-password.php?token=" . $reset_token;
                    
                    // In a real application, you would send an email here
                    // For demo purposes, we'll just show the link
                    $message = "Password reset link: <a href='$reset_link' target='_blank'>$reset_link</a><br>";
                    $message .= "This link will expire in 1 hour.";
                    $message_type = 'success';
                    
                    // Log password reset request
                    $stmt = $db->prepare("
                        INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                        VALUES (?, 'users', ?, 'PASSWORD_RESET_REQUEST', ?, ?, ?)
                    ");
                    $stmt->execute([
                        $user['id'], 
                        $user['id'], 
                        json_encode(['email' => $email, 'token_expires' => $expires]),
                        $_SERVER['REMOTE_ADDR'] ?? '',
                        $_SERVER['HTTP_USER_AGENT'] ?? ''
                    ]);
                } else {
                    // Don't reveal if email exists or not for security
                    $message = 'If the email address exists in our system, you will receive a password reset link.';
                    $message_type = 'info';
                }
            } catch (Exception $e) {
                $message = 'An error occurred. Please try again later.';
                $message_type = 'danger';
                error_log('Forgot password error: ' . $e->getMessage());
            }
        }
    }
}

$page_title = 'Forgot Password';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-key fa-3x mb-3"></i>
                <h3>Forgot Password</h3>
                <p class="mb-0">Enter your email to reset your password</p>
            </div>
            
            <div class="login-body">
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?>" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>
                            Email Address
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                               required autocomplete="email">
                        <div class="invalid-feedback">
                            Please enter a valid email address.
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>
                            Send Reset Link
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <a href="../../login.php" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
