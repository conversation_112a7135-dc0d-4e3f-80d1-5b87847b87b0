<?php
/**
 * Dashboard Page
 * GST e-Invoice Application
 */

require_once 'config/config.php';

// Require login
requireLogin();

$page_title = 'Dashboard';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get dashboard statistics
    $stats = [];
    
    // Total invoices for current user (or all for admin)
    if (isAdmin()) {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM invoices");
        $stmt->execute();
    } else {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM invoices WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $stats['total_invoices'] = $stmt->fetch()['total'];
    
    // Draft invoices
    if (isAdmin()) {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM invoices WHERE status = 'draft'");
        $stmt->execute();
    } else {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM invoices WHERE status = 'draft' AND user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $stats['draft_invoices'] = $stmt->fetch()['total'];
    
    // Submitted invoices
    if (isAdmin()) {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM invoices WHERE status = 'submitted'");
        $stmt->execute();
    } else {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM invoices WHERE status = 'submitted' AND user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $stats['submitted_invoices'] = $stmt->fetch()['total'];
    
    // Total amount this month
    if (isAdmin()) {
        $stmt = $db->prepare("
            SELECT COALESCE(SUM(total_amount), 0) as total 
            FROM invoices 
            WHERE MONTH(date) = MONTH(CURRENT_DATE()) 
            AND YEAR(date) = YEAR(CURRENT_DATE())
            AND status = 'submitted'
        ");
        $stmt->execute();
    } else {
        $stmt = $db->prepare("
            SELECT COALESCE(SUM(total_amount), 0) as total 
            FROM invoices 
            WHERE MONTH(date) = MONTH(CURRENT_DATE()) 
            AND YEAR(date) = YEAR(CURRENT_DATE())
            AND status = 'submitted'
            AND user_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $stats['monthly_amount'] = $stmt->fetch()['total'];
    
    // Recent invoices
    if (isAdmin()) {
        $stmt = $db->prepare("
            SELECT i.*, s.name as supplier_name, r.name as recipient_name, u.name as user_name
            FROM invoices i
            JOIN suppliers s ON i.supplier_id = s.id
            JOIN recipients r ON i.recipient_id = r.id
            JOIN users u ON i.user_id = u.id
            ORDER BY i.created_at DESC
            LIMIT 5
        ");
        $stmt->execute();
    } else {
        $stmt = $db->prepare("
            SELECT i.*, s.name as supplier_name, r.name as recipient_name
            FROM invoices i
            JOIN suppliers s ON i.supplier_id = s.id
            JOIN recipients r ON i.recipient_id = r.id
            WHERE i.user_id = ?
            ORDER BY i.created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $recent_invoices = $stmt->fetchAll();
    
    // Master data counts (for admin)
    if (isAdmin()) {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM suppliers WHERE is_active = 1");
        $stmt->execute();
        $stats['suppliers'] = $stmt->fetch()['total'];
        
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM recipients WHERE is_active = 1");
        $stmt->execute();
        $stats['recipients'] = $stmt->fetch()['total'];
        
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM products WHERE is_active = 1");
        $stmt->execute();
        $stats['products'] = $stmt->fetch()['total'];
        
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
        $stmt->execute();
        $stats['users'] = $stmt->fetch()['total'];
    }
    
} catch (Exception $e) {
    error_log('Dashboard error: ' . $e->getMessage());
    $stats = [
        'total_invoices' => 0,
        'draft_invoices' => 0,
        'submitted_invoices' => 0,
        'monthly_amount' => 0
    ];
    $recent_invoices = [];
}

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-tachometer-alt me-2"></i>
                Dashboard
            </h2>
            <div class="text-muted">
                Welcome back, <?php echo htmlspecialchars($_SESSION['user_name']); ?>!
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card primary">
            <div class="card-body text-center">
                <i class="fas fa-file-invoice card-icon"></i>
                <h3 class="mb-1"><?php echo number_format($stats['total_invoices']); ?></h3>
                <p class="mb-0">Total Invoices</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card warning">
            <div class="card-body text-center">
                <i class="fas fa-edit card-icon"></i>
                <h3 class="mb-1"><?php echo number_format($stats['draft_invoices']); ?></h3>
                <p class="mb-0">Draft Invoices</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle card-icon"></i>
                <h3 class="mb-1"><?php echo number_format($stats['submitted_invoices']); ?></h3>
                <p class="mb-0">Submitted Invoices</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="dashboard-card info">
            <div class="card-body text-center">
                <i class="fas fa-rupee-sign card-icon"></i>
                <h3 class="mb-1"><?php echo formatCurrency($stats['monthly_amount']); ?></h3>
                <p class="mb-0">This Month</p>
            </div>
        </div>
    </div>
</div>

<?php if (isAdmin()): ?>
<!-- Admin Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-primary">
            <div class="card-body text-center">
                <i class="fas fa-building text-primary fa-2x mb-2"></i>
                <h4 class="text-primary"><?php echo number_format($stats['suppliers']); ?></h4>
                <p class="mb-0">Suppliers</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-users text-success fa-2x mb-2"></i>
                <h4 class="text-success"><?php echo number_format($stats['recipients']); ?></h4>
                <p class="mb-0">Recipients</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-box text-warning fa-2x mb-2"></i>
                <h4 class="text-warning"><?php echo number_format($stats['products']); ?></h4>
                <p class="mb-0">Products</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-user-cog text-info fa-2x mb-2"></i>
                <h4 class="text-info"><?php echo number_format($stats['users']); ?></h4>
                <p class="mb-0">Users</p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="modules/invoices/create.php" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            New Invoice
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="modules/masters/suppliers.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-building me-2"></i>
                            Manage Suppliers
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="modules/masters/recipients.php" class="btn btn-outline-success w-100">
                            <i class="fas fa-users me-2"></i>
                            Manage Recipients
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="modules/reports/index.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-chart-bar me-2"></i>
                            View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Invoices -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Recent Invoices
                </h5>
                <a href="modules/invoices/list.php" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_invoices)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Date</th>
                                    <th>Supplier</th>
                                    <th>Recipient</th>
                                    <?php if (isAdmin()): ?>
                                        <th>User</th>
                                    <?php endif; ?>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_invoices as $invoice): ?>
                                    <tr>
                                        <td>
                                            <a href="modules/invoices/view.php?id=<?php echo $invoice['id']; ?>" 
                                               class="text-decoration-none">
                                                <?php echo htmlspecialchars($invoice['invoice_number']); ?>
                                            </a>
                                        </td>
                                        <td><?php echo formatDate($invoice['date']); ?></td>
                                        <td><?php echo htmlspecialchars($invoice['supplier_name']); ?></td>
                                        <td><?php echo htmlspecialchars($invoice['recipient_name']); ?></td>
                                        <?php if (isAdmin()): ?>
                                            <td><?php echo htmlspecialchars($invoice['user_name']); ?></td>
                                        <?php endif; ?>
                                        <td><?php echo formatCurrency($invoice['total_amount']); ?></td>
                                        <td>
                                            <span class="badge status-<?php echo $invoice['status']; ?>">
                                                <?php echo ucfirst($invoice['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No invoices found</h5>
                        <p class="text-muted">Create your first invoice to get started.</p>
                        <a href="modules/invoices/create.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create Invoice
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
