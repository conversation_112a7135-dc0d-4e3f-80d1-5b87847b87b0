-- GST e-Invoice Application Database Schema
-- Created: 2025-06-30
-- Version: 1.0

-- Create database
CREATE DATABASE IF NOT EXISTS gst_einvoice CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE gst_einvoice;

-- Users table for authentication and authorization
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'standard') DEFAULT 'standard',
    failed_attempts TINYINT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    reset_token VARCHAR(100) NULL,
    reset_token_expires TIMESTAMP NULL,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_reset_token (reset_token)
);

-- Suppliers table for supplier master data
CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    state VARCHAR(50),
    gstin VARCHAR(15) UNIQUE,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_gstin (gstin),
    INDEX idx_name (name),
    INDEX idx_state (state),
    INDEX idx_active (is_active)
);

-- Recipients table for recipient/customer master data
CREATE TABLE recipients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    state VARCHAR(50),
    gstin VARCHAR(15) UNIQUE,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_gstin (gstin),
    INDEX idx_name (name),
    INDEX idx_state (state),
    INDEX idx_active (is_active)
);

-- Products table for product master data
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    hsn_sac_code VARCHAR(8),
    unit ENUM('Nos', 'Kg', 'Ltr', 'Mtr', 'Box', 'Pcs', 'Set', 'Pair') NOT NULL DEFAULT 'Nos',
    tax_rate DECIMAL(5,2) NOT NULL CHECK (tax_rate IN (0, 5, 12, 18, 28)),
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_code (code),
    INDEX idx_name (name),
    INDEX idx_hsn (hsn_sac_code),
    INDEX idx_tax_rate (tax_rate),
    INDEX idx_active (is_active)
);

-- Invoices table for invoice header data
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    user_id INT NOT NULL,
    supplier_id INT NOT NULL,
    recipient_id INT NOT NULL,
    date DATE NOT NULL,
    due_date DATE NULL,
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0,
    cgst_amount DECIMAL(12,2) DEFAULT 0,
    sgst_amount DECIMAL(12,2) DEFAULT 0,
    igst_amount DECIMAL(12,2) DEFAULT 0,
    total_tax DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    irn VARCHAR(100) NULL,
    ack_number VARCHAR(50) NULL,
    ack_date DATETIME NULL,
    qr_code TEXT NULL,
    status ENUM('draft', 'submitted', 'cancelled') DEFAULT 'draft',
    json_data LONGTEXT NOT NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (recipient_id) REFERENCES recipients(id),
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_user_id (user_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_recipient_id (recipient_id),
    INDEX idx_date (date),
    INDEX idx_status (status),
    INDEX idx_irn (irn)
);

-- Invoice Items table for invoice line items
CREATE TABLE invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    rate DECIMAL(10,2) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    taxable_amount DECIMAL(10,2) NOT NULL,
    tax_rate DECIMAL(5,2) NOT NULL,
    cgst_rate DECIMAL(5,2) DEFAULT 0,
    sgst_rate DECIMAL(5,2) DEFAULT 0,
    igst_rate DECIMAL(5,2) DEFAULT 0,
    cgst_amount DECIMAL(10,2) DEFAULT 0,
    sgst_amount DECIMAL(10,2) DEFAULT 0,
    igst_amount DECIMAL(10,2) DEFAULT 0,
    total_tax DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_product_id (product_id)
);

-- Audit log table for tracking changes
CREATE TABLE audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    table_name VARCHAR(50) NOT NULL,
    record_id INT NOT NULL,
    action ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_table_name (table_name),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- System settings table
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_setting_key (setting_key)
);

-- Insert default admin user
INSERT INTO users (name, email, password, role) VALUES 
('Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert default demo user
INSERT INTO users (name, email, password, role) VALUES 
('Demo User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'standard');

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('company_name', 'Your Company Name', 'Default company name for invoices'),
('company_address', 'Your Company Address', 'Default company address'),
('company_gstin', '29ABCDE1234F1Z5', 'Default company GSTIN'),
('invoice_prefix', 'INV', 'Prefix for auto-generated invoice numbers'),
('invoice_start_number', '1', 'Starting number for invoice sequence'),
('tax_calculation_method', 'exclusive', 'Tax calculation method (inclusive/exclusive)'),
('default_currency', 'INR', 'Default currency for invoices'),
('nic_api_url', 'https://gsp.adaequare.com/test/enriched/ei/api/invoice', 'NIC API URL for e-invoice submission'),
('use_trial_mode', '1', 'Use trial mode for NIC API (1=yes, 0=no)');

-- Create views for reporting
CREATE VIEW invoice_summary AS
SELECT 
    i.id,
    i.invoice_number,
    i.date,
    s.name as supplier_name,
    s.gstin as supplier_gstin,
    r.name as recipient_name,
    r.gstin as recipient_gstin,
    i.subtotal,
    i.total_tax,
    i.total_amount,
    i.status,
    i.irn,
    u.name as created_by
FROM invoices i
JOIN suppliers s ON i.supplier_id = s.id
JOIN recipients r ON i.recipient_id = r.id
JOIN users u ON i.user_id = u.id;

-- Create security log table
CREATE TABLE security_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    description TEXT,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_event_type (event_type),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create stored procedure for generating invoice numbers
DELIMITER //
CREATE PROCEDURE GenerateInvoiceNumber(OUT new_invoice_number VARCHAR(50))
BEGIN
    DECLARE prefix VARCHAR(10);
    DECLARE start_num INT;
    DECLARE next_num INT;
    DECLARE date_part VARCHAR(8);
    
    SELECT setting_value INTO prefix FROM system_settings WHERE setting_key = 'invoice_prefix';
    SELECT setting_value INTO start_num FROM system_settings WHERE setting_key = 'invoice_start_number';
    
    SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
    
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number, -3) AS UNSIGNED)), start_num - 1) + 1 
    INTO next_num 
    FROM invoices 
    WHERE invoice_number LIKE CONCAT(prefix, '-', date_part, '-%');
    
    SET new_invoice_number = CONCAT(prefix, '-', date_part, '-', LPAD(next_num, 3, '0'));
END //
DELIMITER ;
