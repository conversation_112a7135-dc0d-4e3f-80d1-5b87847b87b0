<?php
/**
 * NIC Schema JSON Generation
 * GST e-Invoice Application - Ver 1.1 Compliance
 */

require_once '../../config/config.php';

// Require login
requireLogin();

// Get invoice ID
$invoice_id = (int)($_GET['id'] ?? 0);

if (!$invoice_id) {
    $_SESSION['error_message'] = 'Invalid invoice ID.';
    header('Location: list.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get invoice with related data
    $stmt = $db->prepare("
        SELECT i.*, 
               s.name as supplier_name, s.address as supplier_address, s.state as supplier_state, 
               s.gstin as supplier_gstin, s.contact_person as supplier_contact, s.email as supplier_email, s.phone as supplier_phone,
               r.name as recipient_name, r.address as recipient_address, r.state as recipient_state, 
               r.gstin as recipient_gstin, r.contact_person as recipient_contact, r.email as recipient_email, 
               r.phone as recipient_phone, r.pincode as recipient_pincode,
               u.name as created_by_name
        FROM invoices i
        JOIN suppliers s ON i.supplier_id = s.id
        JOIN recipients r ON i.recipient_id = r.id
        JOIN users u ON i.user_id = u.id
        WHERE i.id = ?
    ");
    $stmt->execute([$invoice_id]);
    $invoice = $stmt->fetch();
    
    if (!$invoice) {
        $_SESSION['error_message'] = 'Invoice not found.';
        header('Location: list.php');
        exit();
    }
    
    // Check access permissions
    if (!isAdmin() && $invoice['user_id'] != $_SESSION['user_id']) {
        $_SESSION['error_message'] = 'Access denied.';
        header('Location: list.php');
        exit();
    }
    
    // Get invoice items
    $stmt = $db->prepare("
        SELECT ii.*, p.name as product_name
        FROM invoice_items ii
        LEFT JOIN products p ON ii.product_id = p.id
        WHERE ii.invoice_id = ?
        ORDER BY ii.id
    ");
    $stmt->execute([$invoice_id]);
    $items = $stmt->fetchAll();
    
    // Generate NIC Schema Ver 1.1 JSON
    $json_data = [
        "Version" => "1.1",
        "TranDtls" => [
            "TaxSch" => "GST",
            "SupTyp" => "B2B",
            "RegRev" => "N",
            "EcmGstin" => null,
            "IgstOnIntra" => "N"
        ],
        "DocDtls" => [
            "Typ" => "INV",
            "No" => $invoice['invoice_number'],
            "Dt" => date('d/m/Y', strtotime($invoice['date']))
        ],
        "SellerDtls" => [
            "Gstin" => $invoice['supplier_gstin'] ?: "URP",
            "LglNm" => $invoice['supplier_name'],
            "TrdNm" => $invoice['supplier_name'],
            "Addr1" => $invoice['supplier_address'] ?: "",
            "Addr2" => "",
            "Loc" => $invoice['supplier_state'],
            "Pin" => 000000, // Default pincode if not available
            "Stcd" => $this->getStateCode($invoice['supplier_state']),
            "Ph" => $invoice['supplier_phone'] ?: "",
            "Em" => $invoice['supplier_email'] ?: ""
        ],
        "BuyerDtls" => [
            "Gstin" => $invoice['recipient_gstin'] ?: "URP",
            "LglNm" => $invoice['recipient_name'],
            "TrdNm" => $invoice['recipient_name'],
            "Pos" => $this->getStateCode($invoice['place_of_supply']),
            "Addr1" => $invoice['recipient_address'] ?: "",
            "Addr2" => "",
            "Loc" => $invoice['recipient_state'],
            "Pin" => (int)($invoice['recipient_pincode'] ?: 000000),
            "Stcd" => $this->getStateCode($invoice['recipient_state']),
            "Ph" => $invoice['recipient_phone'] ?: "",
            "Em" => $invoice['recipient_email'] ?: ""
        ],
        "DispDtls" => [
            "Nm" => $invoice['supplier_name'],
            "Addr1" => $invoice['supplier_address'] ?: "",
            "Addr2" => "",
            "Loc" => $invoice['supplier_state'],
            "Pin" => 000000,
            "Stcd" => $this->getStateCode($invoice['supplier_state'])
        ],
        "ShipDtls" => [
            "Gstin" => $invoice['recipient_gstin'] ?: "URP",
            "LglNm" => $invoice['recipient_name'],
            "TrdNm" => $invoice['recipient_name'],
            "Addr1" => $invoice['recipient_address'] ?: "",
            "Addr2" => "",
            "Loc" => $invoice['recipient_state'],
            "Pin" => (int)($invoice['recipient_pincode'] ?: 000000),
            "Stcd" => $this->getStateCode($invoice['recipient_state'])
        ],
        "ItemList" => [],
        "ValDtls" => [
            "AssVal" => round($invoice['subtotal'], 2),
            "CgstVal" => round($invoice['cgst_amount'], 2),
            "SgstVal" => round($invoice['sgst_amount'], 2),
            "IgstVal" => round($invoice['igst_amount'], 2),
            "CesVal" => 0,
            "StCesVal" => 0,
            "Discount" => 0,
            "OthChrg" => 0,
            "RndOffAmt" => 0,
            "TotInvVal" => round($invoice['total_amount'], 2),
            "TotInvValFc" => round($invoice['total_amount'], 2)
        ],
        "PayDtls" => [
            "Nm" => $invoice['recipient_name'],
            "AccDet" => "",
            "Mode" => "",
            "FinInsBr" => "",
            "PayTerm" => "",
            "PayInstr" => "",
            "CrTrn" => "",
            "DirDr" => "",
            "CrDay" => 0,
            "PaidAmt" => 0,
            "PaymtDue" => round($invoice['total_amount'], 2)
        ],
        "RefDtls" => [
            "InvRm" => $invoice['notes'] ?: "",
            "DocPerdDtls" => [
                "InvStDt" => date('d/m/Y', strtotime($invoice['date'])),
                "InvEndDt" => date('d/m/Y', strtotime($invoice['date']))
            ],
            "PrecDocDtls" => []
        ],
        "AddlDocDtls" => [],
        "ExpDtls" => null,
        "EwbDtls" => null
    ];
    
    // Add items to JSON
    $sl_no = 1;
    foreach ($items as $item) {
        $item_data = [
            "SlNo" => (string)$sl_no,
            "PrdDesc" => $item['description'],
            "IsServc" => "N",
            "HsnCd" => $item['hsn_sac'],
            "Barcde" => "",
            "Qty" => round($item['quantity'], 3),
            "FreeQty" => 0,
            "Unit" => strtoupper($item['unit']),
            "UnitPrice" => round($item['rate'], 2),
            "TotAmt" => round($item['amount'], 2),
            "Discount" => 0,
            "PreTaxVal" => round($item['amount'], 2),
            "AssAmt" => round($item['amount'], 2),
            "GstRt" => round($item['gst_rate'], 2),
            "IgstAmt" => round($item['igst_amount'], 2),
            "CgstAmt" => round($item['cgst_amount'], 2),
            "SgstAmt" => round($item['sgst_amount'], 2),
            "CesRt" => 0,
            "CesAmt" => 0,
            "CesNonAdvlAmt" => 0,
            "StateCesRt" => 0,
            "StateCesAmt" => 0,
            "StateCesNonAdvlAmt" => 0,
            "OthChrg" => 0,
            "TotItemVal" => round($item['amount'] + $item['cgst_amount'] + $item['sgst_amount'] + $item['igst_amount'], 2),
            "OrdLineRef" => "",
            "OrgCntry" => "IN",
            "PrdSlNo" => "",
            "BchDtls" => [
                "Nm" => "",
                "ExpDt" => "",
                "WrDt" => ""
            ],
            "AttribDtls" => []
        ];
        
        $json_data["ItemList"][] = $item_data;
        $sl_no++;
    }
    
    // Set headers for JSON download
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="invoice_' . $invoice['invoice_number'] . '_nic_schema.json"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    // Output JSON
    echo json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit();
    
} catch (Exception $e) {
    error_log('JSON generation error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while generating JSON.';
    header('Location: view.php?id=' . $invoice_id);
    exit();
}

/**
 * Get state code for GST
 */
function getStateCode($state) {
    $state_codes = [
        'Andhra Pradesh' => '37',
        'Arunachal Pradesh' => '12',
        'Assam' => '18',
        'Bihar' => '10',
        'Chhattisgarh' => '22',
        'Goa' => '30',
        'Gujarat' => '24',
        'Haryana' => '06',
        'Himachal Pradesh' => '02',
        'Jharkhand' => '20',
        'Karnataka' => '29',
        'Kerala' => '32',
        'Madhya Pradesh' => '23',
        'Maharashtra' => '27',
        'Manipur' => '14',
        'Meghalaya' => '17',
        'Mizoram' => '15',
        'Nagaland' => '13',
        'Odisha' => '21',
        'Punjab' => '03',
        'Rajasthan' => '08',
        'Sikkim' => '11',
        'Tamil Nadu' => '33',
        'Telangana' => '36',
        'Tripura' => '16',
        'Uttar Pradesh' => '09',
        'Uttarakhand' => '05',
        'West Bengal' => '19',
        'Delhi' => '07',
        'Chandigarh' => '04',
        'Puducherry' => '34'
    ];
    
    return $state_codes[$state] ?? '99';
}
?>
