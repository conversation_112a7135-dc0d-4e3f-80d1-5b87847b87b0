<?php
/**
 * GST Summary Report
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login
requireLogin();

$page_title = 'GST Summary Report';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get date range from request
    $date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
    $date_to = $_GET['date_to'] ?? date('Y-m-t'); // Last day of current month
    $state_filter = $_GET['state'] ?? '';
    
    // User condition for non-admin users
    $user_condition = '';
    $user_params = [];
    if (!isAdmin()) {
        $user_condition = ' AND i.user_id = ?';
        $user_params[] = $_SESSION['user_id'];
    }
    
    // Get GST summary data
    $gst_summary_query = "
        SELECT 
            i.place_of_supply,
            COUNT(i.id) as invoice_count,
            SUM(i.subtotal) as total_taxable_amount,
            SUM(i.cgst_amount) as total_cgst,
            SUM(i.sgst_amount) as total_sgst,
            SUM(i.igst_amount) as total_igst,
            SUM(i.total_amount) as total_amount
        FROM invoices i
        WHERE i.date BETWEEN ? AND ?
        AND i.status = 'submitted'
        $user_condition
    ";
    
    $params = [$date_from, $date_to];
    $params = array_merge($params, $user_params);
    
    if (!empty($state_filter)) {
        $gst_summary_query .= " AND i.place_of_supply = ?";
        $params[] = $state_filter;
    }
    
    $gst_summary_query .= " GROUP BY i.place_of_supply ORDER BY total_amount DESC";
    
    $stmt = $db->prepare($gst_summary_query);
    $stmt->execute($params);
    $gst_summary = $stmt->fetchAll();
    
    // Get overall totals
    $totals_query = "
        SELECT 
            COUNT(i.id) as total_invoices,
            SUM(i.subtotal) as total_taxable,
            SUM(i.cgst_amount) as total_cgst,
            SUM(i.sgst_amount) as total_sgst,
            SUM(i.igst_amount) as total_igst,
            SUM(i.total_amount) as grand_total
        FROM invoices i
        WHERE i.date BETWEEN ? AND ?
        AND i.status = 'submitted'
        $user_condition
    ";
    
    $totals_params = [$date_from, $date_to];
    $totals_params = array_merge($totals_params, $user_params);
    
    if (!empty($state_filter)) {
        $totals_query .= " AND i.place_of_supply = ?";
        $totals_params[] = $state_filter;
    }
    
    $stmt = $db->prepare($totals_query);
    $stmt->execute($totals_params);
    $totals = $stmt->fetch();
    
    // Get state list for filter
    $states = getIndianStates();
    
} catch (Exception $e) {
    error_log('GST Summary error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while generating the GST summary.';
    $gst_summary = [];
    $totals = [];
}

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-file-alt me-2"></i>
                GST Summary Report
            </h2>
            <div>
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Reports
                </a>
                <button onclick="window.print()" class="btn btn-outline-primary">
                    <i class="fas fa-print me-2"></i>
                    Print
                </button>
                <a href="export.php?type=gst_summary&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&state=<?php echo $state_filter; ?>" class="btn btn-success">
                    <i class="fas fa-download me-2"></i>
                    Export CSV
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>" required>
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>" required>
                    </div>
                    <div class="col-md-3">
                        <label for="state" class="form-label">State Filter</label>
                        <select class="form-select" id="state" name="state">
                            <option value="">All States</option>
                            <?php foreach ($states as $code => $name): ?>
                                <option value="<?php echo $name; ?>" <?php echo $state_filter === $name ? 'selected' : ''; ?>>
                                    <?php echo $name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                Generate Report
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<?php if (!empty($totals)): ?>
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary"><?php echo number_format($totals['total_invoices']); ?></h5>
                <p class="card-text">Total Invoices</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">₹<?php echo number_format($totals['total_taxable'], 2); ?></h5>
                <p class="card-text">Taxable Amount</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">₹<?php echo number_format($totals['total_cgst'], 2); ?></h5>
                <p class="card-text">Total CGST</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">₹<?php echo number_format($totals['total_sgst'], 2); ?></h5>
                <p class="card-text">Total SGST</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">₹<?php echo number_format($totals['total_igst'], 2); ?></h5>
                <p class="card-text">Total IGST</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-dark">₹<?php echo number_format($totals['grand_total'], 2); ?></h5>
                <p class="card-text">Grand Total</p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- GST Summary Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    State-wise GST Summary (<?php echo date('d M Y', strtotime($date_from)); ?> to <?php echo date('d M Y', strtotime($date_to)); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($gst_summary)): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>State/UT</th>
                                <th class="text-end">Invoices</th>
                                <th class="text-end">Taxable Amount</th>
                                <th class="text-end">CGST</th>
                                <th class="text-end">SGST</th>
                                <th class="text-end">IGST</th>
                                <th class="text-end">Total Tax</th>
                                <th class="text-end">Total Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($gst_summary as $row): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['place_of_supply']); ?></td>
                                <td class="text-end"><?php echo number_format($row['invoice_count']); ?></td>
                                <td class="text-end">₹<?php echo number_format($row['total_taxable_amount'], 2); ?></td>
                                <td class="text-end">₹<?php echo number_format($row['total_cgst'], 2); ?></td>
                                <td class="text-end">₹<?php echo number_format($row['total_sgst'], 2); ?></td>
                                <td class="text-end">₹<?php echo number_format($row['total_igst'], 2); ?></td>
                                <td class="text-end">₹<?php echo number_format($row['total_cgst'] + $row['total_sgst'] + $row['total_igst'], 2); ?></td>
                                <td class="text-end"><strong>₹<?php echo number_format($row['total_amount'], 2); ?></strong></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot class="table-secondary">
                            <tr>
                                <th>Total</th>
                                <th class="text-end"><?php echo number_format($totals['total_invoices']); ?></th>
                                <th class="text-end">₹<?php echo number_format($totals['total_taxable'], 2); ?></th>
                                <th class="text-end">₹<?php echo number_format($totals['total_cgst'], 2); ?></th>
                                <th class="text-end">₹<?php echo number_format($totals['total_sgst'], 2); ?></th>
                                <th class="text-end">₹<?php echo number_format($totals['total_igst'], 2); ?></th>
                                <th class="text-end">₹<?php echo number_format($totals['total_cgst'] + $totals['total_sgst'] + $totals['total_igst'], 2); ?></th>
                                <th class="text-end"><strong>₹<?php echo number_format($totals['grand_total'], 2); ?></strong></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No data found for the selected period</h5>
                    <p class="text-muted">Try adjusting the date range or filters.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header, .navbar, .sidebar { display: none !important; }
    .card { border: none !important; box-shadow: none !important; }
    .table { font-size: 12px; }
}
</style>

<?php include '../../includes/footer.php'; ?>
