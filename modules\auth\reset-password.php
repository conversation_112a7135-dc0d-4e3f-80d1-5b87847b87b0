<?php
/**
 * Reset Password Page
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ../../dashboard.php');
    exit();
}

$token = $_GET['token'] ?? '';
$message = '';
$message_type = '';
$valid_token = false;
$user_id = null;

// Validate token
if (!empty($token)) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $stmt = $db->prepare("
            SELECT id, name, email 
            FROM users 
            WHERE reset_token = ? AND reset_token_expires > NOW() AND is_active = 1
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch();
        
        if ($user) {
            $valid_token = true;
            $user_id = $user['id'];
        } else {
            $message = 'Invalid or expired reset token. Please request a new password reset.';
            $message_type = 'danger';
        }
    } catch (Exception $e) {
        $message = 'An error occurred. Please try again later.';
        $message_type = 'danger';
        error_log('Reset password token validation error: ' . $e->getMessage());
    }
} else {
    $message = 'No reset token provided.';
    $message_type = 'danger';
}

// Process password reset
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $valid_token) {
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Validate CSRF token
    if (!validateCSRFToken($csrf_token)) {
        $message = 'Invalid request. Please try again.';
        $message_type = 'danger';
    } else {
        // Validate passwords
        if (empty($password) || strlen($password) < 6) {
            $message = 'Password must be at least 6 characters long.';
            $message_type = 'danger';
        } elseif ($password !== $confirm_password) {
            $message = 'Passwords do not match.';
            $message_type = 'danger';
        } else {
            try {
                $database = new Database();
                $db = $database->getConnection();
                
                // Hash new password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Update password and clear reset token
                $stmt = $db->prepare("
                    UPDATE users 
                    SET password = ?, reset_token = NULL, reset_token_expires = NULL, failed_attempts = 0, locked_until = NULL
                    WHERE id = ?
                ");
                $stmt->execute([$hashed_password, $user_id]);
                
                // Log password reset
                $stmt = $db->prepare("
                    INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                    VALUES (?, 'users', ?, 'PASSWORD_RESET', ?, ?, ?)
                ");
                $stmt->execute([
                    $user_id, 
                    $user_id, 
                    json_encode(['reset_time' => date('Y-m-d H:i:s')]),
                    $_SERVER['REMOTE_ADDR'] ?? '',
                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);
                
                $message = 'Password reset successfully! You can now login with your new password.';
                $message_type = 'success';
                $valid_token = false; // Prevent form from showing again
                
            } catch (Exception $e) {
                $message = 'An error occurred while resetting password. Please try again.';
                $message_type = 'danger';
                error_log('Reset password error: ' . $e->getMessage());
            }
        }
    }
}

$page_title = 'Reset Password';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-lock fa-3x mb-3"></i>
                <h3>Reset Password</h3>
                <p class="mb-0">Enter your new password</p>
            </div>
            
            <div class="login-body">
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?>" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($valid_token): ?>
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                New Password
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       minlength="6" required autocomplete="new-password">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Password must be at least 6 characters long.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                Confirm New Password
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       minlength="6" required autocomplete="new-password">
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Please confirm your password.
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>
                                Reset Password
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
                
                <hr>
                
                <div class="text-center">
                    <a href="../../login.php" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // Toggle password visibility
        function togglePasswordVisibility(inputId, buttonId) {
            document.getElementById(buttonId).addEventListener('click', function() {
                const password = document.getElementById(inputId);
                const icon = this.querySelector('i');
                
                if (password.type === 'password') {
                    password.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    password.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }
        
        togglePasswordVisibility('password', 'togglePassword');
        togglePasswordVisibility('confirm_password', 'toggleConfirmPassword');
        
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
