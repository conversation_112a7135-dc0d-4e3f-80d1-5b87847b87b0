<?php
/**
 * Application Bug Fix and Enhancement Script
 * GST e-Invoice Application
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';

echo "<h1>🔧 GST e-Invoice Application - Bug Fix & Enhancement</h1>";
echo "<style>
body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
.success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
.fix { background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0; }
h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
</style>";

$fixes_applied = [];
$issues_found = [];

// Fix 1: Check and create missing files
echo "<h3>🔍 Fix 1: Missing Files Check</h3>";

$required_files = [
    'modules/reports/index.php' => 'Reports index page',
    'modules/auth/login.php' => 'Auth login module',
    'assets/css/style.css' => 'Main stylesheet',
    'assets/js/app.js' => 'Main JavaScript file'
];

foreach ($required_files as $file => $description) {
    if (!file_exists($file)) {
        echo "<div class='warning'>⚠️ Missing: {$description} ({$file})</div>";
        $issues_found[] = "Missing file: {$file}";
        
        // Auto-create some missing files
        if ($file === 'modules/reports/index.php' && file_exists('modules/reports/dashboard.php')) {
            copy('modules/reports/dashboard.php', 'modules/reports/index.php');
            echo "<div class='success'>✅ Created {$file} by copying dashboard.php</div>";
            $fixes_applied[] = "Created {$file}";
        }
        
        if ($file === 'modules/auth/login.php' && file_exists('login.php')) {
            // Create a redirect file
            $content = "<?php\n// Redirect to main login\nheader('Location: ../../login.php');\nexit();\n?>";
            file_put_contents($file, $content);
            echo "<div class='success'>✅ Created {$file} as redirect</div>";
            $fixes_applied[] = "Created {$file}";
        }
    } else {
        echo "<div class='success'>✅ Found: {$description}</div>";
    }
}

// Fix 2: Database integrity check
echo "<h3>🗄️ Fix 2: Database Integrity Check</h3>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        // Check for admin user
        $stmt = $db->prepare("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if (!$admin) {
            echo "<div class='error'>❌ No admin user found</div>";
            $issues_found[] = "No admin user in database";
            
            // Create admin user
            $admin_email = '<EMAIL>';
            $admin_password = hashPassword('Admin@123');
            $admin_name = 'System Administrator';
            
            $stmt = $db->prepare("INSERT INTO users (name, email, password, role, status, created_at) VALUES (?, ?, ?, 'admin', 'active', NOW())");
            if ($stmt->execute([$admin_name, $admin_email, $admin_password])) {
                echo "<div class='success'>✅ Created admin user: {$admin_email} / Admin@123</div>";
                $fixes_applied[] = "Created admin user";
            }
        } else {
            echo "<div class='success'>✅ Admin user exists: {$admin['email']}</div>";
        }
        
        // Check for sample data
        $tables_to_check = ['suppliers', 'recipients', 'products'];
        foreach ($tables_to_check as $table) {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM {$table}");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            
            if ($count == 0) {
                echo "<div class='warning'>⚠️ No sample data in {$table} table</div>";
                $issues_found[] = "No sample data in {$table}";
                
                // Add sample data
                if ($table === 'suppliers') {
                    addSampleSupplier($db);
                } elseif ($table === 'recipients') {
                    addSampleRecipient($db);
                } elseif ($table === 'products') {
                    addSampleProduct($db);
                }
            } else {
                echo "<div class='success'>✅ {$table}: {$count} records</div>";
            }
        }
        
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
        $issues_found[] = "Database connection failed";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
    $issues_found[] = "Database error: " . $e->getMessage();
}

// Fix 3: Function availability check
echo "<h3>⚙️ Fix 3: Function Availability Check</h3>";

$required_functions = [
    'isLoggedIn' => 'Session management',
    'isAdmin' => 'Admin check',
    'requireLogin' => 'Login requirement',
    'hashPassword' => 'Password hashing',
    'verifyPassword' => 'Password verification',
    'validateGSTIN' => 'GSTIN validation',
    'validateEmail' => 'Email validation',
    'calculateTax' => 'Tax calculation',
    'generateCSRFToken' => 'CSRF token generation',
    'validateCSRFToken' => 'CSRF token validation',
    'sanitizeInput' => 'Input sanitization',
    'logSecurityEvent' => 'Security logging'
];

$missing_functions = [];
foreach ($required_functions as $func => $description) {
    if (function_exists($func)) {
        echo "<div class='success'>✅ {$description} ({$func})</div>";
    } else {
        echo "<div class='error'>❌ Missing: {$description} ({$func})</div>";
        $missing_functions[] = $func;
        $issues_found[] = "Missing function: {$func}";
    }
}

// Fix 4: Test core functionality
echo "<h3>🧪 Fix 4: Core Functionality Test</h3>";

// Test tax calculation
if (function_exists('calculateTax')) {
    try {
        $test_amount = 1000;
        $test_rate = 18;
        
        // Same state test
        $result = calculateTax($test_amount, $test_rate, 'Maharashtra', 'Maharashtra');
        if (isset($result['cgst_amount']) && $result['cgst_amount'] == 90) {
            echo "<div class='success'>✅ Same state tax calculation working</div>";
        } else {
            echo "<div class='error'>❌ Same state tax calculation failed</div>";
            $issues_found[] = "Tax calculation error (same state)";
        }
        
        // Different state test
        $result = calculateTax($test_amount, $test_rate, 'Maharashtra', 'Karnataka');
        if (isset($result['igst_amount']) && $result['igst_amount'] == 180) {
            echo "<div class='success'>✅ Different state tax calculation working</div>";
        } else {
            echo "<div class='error'>❌ Different state tax calculation failed</div>";
            $issues_found[] = "Tax calculation error (different state)";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Tax calculation test failed: " . $e->getMessage() . "</div>";
        $issues_found[] = "Tax calculation exception: " . $e->getMessage();
    }
} else {
    echo "<div class='error'>❌ Tax calculation function not available</div>";
}

// Test GSTIN validation
if (function_exists('validateGSTIN')) {
    $valid_gstin = '27AAPFU0939F1ZV';
    $invalid_gstin = 'INVALID123';
    
    if (validateGSTIN($valid_gstin) && !validateGSTIN($invalid_gstin)) {
        echo "<div class='success'>✅ GSTIN validation working</div>";
    } else {
        echo "<div class='error'>❌ GSTIN validation failed</div>";
        $issues_found[] = "GSTIN validation error";
    }
} else {
    echo "<div class='error'>❌ GSTIN validation function not available</div>";
}

// Fix 5: Create missing CSS and JS files
echo "<h3>🎨 Fix 5: Assets Check</h3>";

if (!file_exists('assets/css/style.css')) {
    echo "<div class='warning'>⚠️ Main stylesheet missing</div>";
    // Create basic CSS file
    $css_content = "/* GST e-Invoice Application Styles */\n.navbar-brand { font-weight: bold; }\n.card { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }\n.btn { border-radius: 4px; }\n.table th { background-color: #f8f9fa; }";
    if (!is_dir('assets/css')) mkdir('assets/css', 0755, true);
    file_put_contents('assets/css/style.css', $css_content);
    echo "<div class='success'>✅ Created basic stylesheet</div>";
    $fixes_applied[] = "Created assets/css/style.css";
}

if (!file_exists('assets/js/app.js')) {
    echo "<div class='warning'>⚠️ Main JavaScript file missing</div>";
    // Create basic JS file
    $js_content = "// GST e-Invoice Application JavaScript\n$(document).ready(function() {\n    // Initialize tooltips\n    $('[data-bs-toggle=\"tooltip\"]').tooltip();\n    \n    // Form validation\n    $('.needs-validation').on('submit', function(e) {\n        if (!this.checkValidity()) {\n            e.preventDefault();\n            e.stopPropagation();\n        }\n        $(this).addClass('was-validated');\n    });\n});";
    if (!is_dir('assets/js')) mkdir('assets/js', 0755, true);
    file_put_contents('assets/js/app.js', $js_content);
    echo "<div class='success'>✅ Created basic JavaScript file</div>";
    $fixes_applied[] = "Created assets/js/app.js";
}

// Summary
echo "<h3>📋 Fix Summary</h3>";

echo "<div class='fix'>";
echo "<h4>✅ Fixes Applied (" . count($fixes_applied) . ")</h4>";
if (empty($fixes_applied)) {
    echo "<p>No fixes were needed - application is in good condition!</p>";
} else {
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>{$fix}</li>";
    }
    echo "</ul>";
}
echo "</div>";

echo "<div class='fix'>";
echo "<h4>⚠️ Issues Found (" . count($issues_found) . ")</h4>";
if (empty($issues_found)) {
    echo "<p>No critical issues found!</p>";
} else {
    echo "<ul>";
    foreach ($issues_found as $issue) {
        echo "<li>{$issue}</li>";
    }
    echo "</ul>";
}
echo "</div>";

echo "<h3>🚀 Next Steps</h3>";
echo "<div class='info'>";
echo "<ol>";
echo "<li><a href='reset_passwords.php'>Reset user passwords</a> if needed</li>";
echo "<li><a href='test_login.php'>Test login functionality</a></li>";
echo "<li><a href='index.php'>Launch the main application</a></li>";
echo "<li>Test all modules: Dashboard → Masters → Invoices → Reports</li>";
echo "<li>Create a test invoice and verify tax calculations</li>";
echo "<li>Test NIC JSON generation and export functionality</li>";
echo "</ol>";
echo "</div>";

// Helper functions
function addSampleSupplier($db) {
    try {
        $stmt = $db->prepare("INSERT INTO suppliers (name, address, state, pincode, gstin, email, phone, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute([
            'Sample Supplier Pvt Ltd',
            '123 Business Street, Commercial Area',
            'Maharashtra',
            '400001',
            '27AAPFU0939F1ZV',
            '<EMAIL>',
            '9876543210'
        ]);
        echo "<div class='success'>✅ Added sample supplier</div>";
        return true;
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to add sample supplier: " . $e->getMessage() . "</div>";
        return false;
    }
}

function addSampleRecipient($db) {
    try {
        $stmt = $db->prepare("INSERT INTO recipients (name, address, state, pincode, gstin, email, phone, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute([
            'Sample Customer Ltd',
            '456 Customer Road, Business District',
            'Karnataka',
            '560001',
            '29AABCU9603R1ZX',
            '<EMAIL>',
            '9876543211'
        ]);
        echo "<div class='success'>✅ Added sample recipient</div>";
        return true;
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to add sample recipient: " . $e->getMessage() . "</div>";
        return false;
    }
}

function addSampleProduct($db) {
    try {
        $stmt = $db->prepare("INSERT INTO products (name, description, hsn_sac, unit, tax_rate, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $stmt->execute([
            'Sample Product',
            'Sample product for testing',
            '1234',
            'PCS',
            18.00
        ]);
        echo "<div class='success'>✅ Added sample product</div>";
        return true;
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to add sample product: " . $e->getMessage() . "</div>";
        return false;
    }
}
?>
