<?php
/**
 * Invoice Creation Page
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login
requireLogin();

$page_title = 'Create Invoice';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!validateCSRFToken($csrf_token)) {
            $_SESSION['error_message'] = 'Invalid request. Please try again.';
        } else {
            // Get form data
            $supplier_id = (int)($_POST['supplier_id'] ?? 0);
            $recipient_id = (int)($_POST['recipient_id'] ?? 0);
            $invoice_date = $_POST['invoice_date'] ?? '';
            $due_date = $_POST['due_date'] ?? '';
            $place_of_supply = sanitizeInput($_POST['place_of_supply'] ?? '');
            $notes = sanitizeInput($_POST['notes'] ?? '');
            $items = $_POST['items'] ?? [];
            
            // Validation
            if (empty($supplier_id) || empty($recipient_id) || empty($invoice_date) || empty($place_of_supply)) {
                $_SESSION['error_message'] = 'Supplier, recipient, date, and place of supply are required.';
            } elseif (empty($items)) {
                $_SESSION['error_message'] = 'At least one item is required.';
            } else {
                $db->beginTransaction();
                
                try {
                    // Generate invoice number
                    $stmt = $db->prepare("CALL GenerateInvoiceNumber(@invoice_number)");
                    $stmt->execute();
                    
                    $stmt = $db->prepare("SELECT @invoice_number as invoice_number");
                    $stmt->execute();
                    $invoice_number = $stmt->fetch()['invoice_number'];
                    
                    // Calculate totals
                    $subtotal = 0;
                    $total_cgst = 0;
                    $total_sgst = 0;
                    $total_igst = 0;
                    
                    // Get supplier and recipient states for tax calculation
                    $stmt = $db->prepare("SELECT state FROM suppliers WHERE id = ?");
                    $stmt->execute([$supplier_id]);
                    $supplier_state = $stmt->fetch()['state'];
                    
                    $stmt = $db->prepare("SELECT state FROM recipients WHERE id = ?");
                    $stmt->execute([$recipient_id]);
                    $recipient_state = $stmt->fetch()['state'];
                    
                    $is_interstate = ($supplier_state !== $recipient_state);
                    
                    foreach ($items as $item) {
                        $quantity = (float)($item['quantity'] ?? 0);
                        $rate = (float)($item['rate'] ?? 0);
                        $gst_rate = (float)($item['gst_rate'] ?? 0);
                        
                        $amount = $quantity * $rate;
                        $subtotal += $amount;
                        
                        if ($is_interstate) {
                            $total_igst += ($amount * $gst_rate / 100);
                        } else {
                            $total_cgst += ($amount * $gst_rate / 200); // Half of GST rate
                            $total_sgst += ($amount * $gst_rate / 200); // Half of GST rate
                        }
                    }
                    
                    $total_amount = $subtotal + $total_cgst + $total_sgst + $total_igst;
                    
                    // Insert invoice
                    $stmt = $db->prepare("
                        INSERT INTO invoices (
                            invoice_number, supplier_id, recipient_id, date, due_date, 
                            place_of_supply, subtotal, cgst_amount, sgst_amount, igst_amount, 
                            total_amount, notes, user_id, status
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft')
                    ");
                    
                    $stmt->execute([
                        $invoice_number, $supplier_id, $recipient_id, $invoice_date, $due_date,
                        $place_of_supply, $subtotal, $total_cgst, $total_sgst, $total_igst,
                        $total_amount, $notes, $_SESSION['user_id']
                    ]);
                    
                    $invoice_id = $db->lastInsertId();
                    
                    // Insert invoice items
                    $stmt = $db->prepare("
                        INSERT INTO invoice_items (
                            invoice_id, product_id, description, hsn_sac, quantity, 
                            unit, rate, amount, gst_rate, cgst_amount, sgst_amount, igst_amount
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    foreach ($items as $item) {
                        $product_id = (int)($item['product_id'] ?? 0);
                        $description = sanitizeInput($item['description'] ?? '');
                        $hsn_sac = sanitizeInput($item['hsn_sac'] ?? '');
                        $quantity = (float)($item['quantity'] ?? 0);
                        $unit = sanitizeInput($item['unit'] ?? '');
                        $rate = (float)($item['rate'] ?? 0);
                        $gst_rate = (float)($item['gst_rate'] ?? 0);
                        
                        $amount = $quantity * $rate;
                        
                        if ($is_interstate) {
                            $igst_amount = $amount * $gst_rate / 100;
                            $cgst_amount = 0;
                            $sgst_amount = 0;
                        } else {
                            $cgst_amount = $amount * $gst_rate / 200;
                            $sgst_amount = $amount * $gst_rate / 200;
                            $igst_amount = 0;
                        }
                        
                        $stmt->execute([
                            $invoice_id, $product_id ?: null, $description, $hsn_sac, $quantity,
                            $unit, $rate, $amount, $gst_rate, $cgst_amount, $sgst_amount, $igst_amount
                        ]);
                    }
                    
                    // Log invoice creation
                    $stmt = $db->prepare("
                        INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                        VALUES (?, 'invoices', ?, 'INSERT', ?, ?, ?)
                    ");
                    $stmt->execute([
                        $_SESSION['user_id'], 
                        $invoice_id, 
                        json_encode(['invoice_number' => $invoice_number, 'total_amount' => $total_amount]),
                        $_SERVER['REMOTE_ADDR'] ?? '',
                        $_SERVER['HTTP_USER_AGENT'] ?? ''
                    ]);
                    
                    $db->commit();
                    
                    $_SESSION['success_message'] = "Invoice $invoice_number created successfully.";
                    header("Location: view.php?id=$invoice_id");
                    exit();
                    
                } catch (Exception $e) {
                    $db->rollback();
                    throw $e;
                }
            }
        }
        
        header('Location: create.php');
        exit();
    }
    
    // Get suppliers for dropdown
    if (isAdmin()) {
        $stmt = $db->prepare("SELECT id, name, gstin FROM suppliers WHERE is_active = 1 ORDER BY name");
    } else {
        $stmt = $db->prepare("SELECT id, name, gstin FROM suppliers WHERE is_active = 1 AND created_by = ? ORDER BY name");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $suppliers = $stmt->fetchAll();
    
    // Get recipients for dropdown
    if (isAdmin()) {
        $stmt = $db->prepare("SELECT id, name, gstin, state FROM recipients WHERE is_active = 1 ORDER BY name");
    } else {
        $stmt = $db->prepare("SELECT id, name, gstin, state FROM recipients WHERE is_active = 1 AND created_by = ? ORDER BY name");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $recipients = $stmt->fetchAll();
    
    // Get products for dropdown
    if (isAdmin()) {
        $stmt = $db->prepare("SELECT id, name, hsn_sac, unit, rate, gst_rate FROM products WHERE is_active = 1 ORDER BY name");
    } else {
        $stmt = $db->prepare("SELECT id, name, hsn_sac, unit, rate, gst_rate FROM products WHERE is_active = 1 AND created_by = ? ORDER BY name");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $products = $stmt->fetchAll();
    
    // Get Indian states for place of supply
    $indian_states = [
        'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa', 'Gujarat',
        'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala', 'Madhya Pradesh',
        'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab',
        'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura', 'Uttar Pradesh',
        'Uttarakhand', 'West Bengal', 'Delhi', 'Chandigarh', 'Puducherry'
    ];
    
} catch (Exception $e) {
    error_log('Invoice creation error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while loading the page.';
    $suppliers = [];
    $recipients = [];
    $products = [];
    $indian_states = [];
}

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-file-invoice me-2"></i>
                Create Invoice
            </h2>
            <a href="list.php" class="btn btn-outline-secondary">
                <i class="fas fa-list me-2"></i>
                View All Invoices
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="POST" id="invoiceForm" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="mb-3">Invoice Details</h5>
                            
                            <div class="mb-3">
                                <label for="supplier_id" class="form-label">Supplier *</label>
                                <select class="form-select" id="supplier_id" name="supplier_id" required>
                                    <option value="">Select Supplier</option>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <option value="<?php echo $supplier['id']; ?>" 
                                                data-gstin="<?php echo htmlspecialchars($supplier['gstin']); ?>">
                                            <?php echo htmlspecialchars($supplier['name']); ?>
                                            <?php if ($supplier['gstin']): ?>
                                                (<?php echo htmlspecialchars($supplier['gstin']); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select a supplier.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="recipient_id" class="form-label">Recipient *</label>
                                <select class="form-select" id="recipient_id" name="recipient_id" required>
                                    <option value="">Select Recipient</option>
                                    <?php foreach ($recipients as $recipient): ?>
                                        <option value="<?php echo $recipient['id']; ?>" 
                                                data-gstin="<?php echo htmlspecialchars($recipient['gstin']); ?>"
                                                data-state="<?php echo htmlspecialchars($recipient['state']); ?>">
                                            <?php echo htmlspecialchars($recipient['name']); ?>
                                            <?php if ($recipient['gstin']): ?>
                                                (<?php echo htmlspecialchars($recipient['gstin']); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select a recipient.</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="mb-3">Date & Location</h5>
                            
                            <div class="mb-3">
                                <label for="invoice_date" class="form-label">Invoice Date *</label>
                                <input type="date" class="form-control" id="invoice_date" name="invoice_date" 
                                       value="<?php echo date('Y-m-d'); ?>" required>
                                <div class="invalid-feedback">Please select invoice date.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="due_date" class="form-label">Due Date</label>
                                <input type="date" class="form-control" id="due_date" name="due_date">
                            </div>
                            
                            <div class="mb-3">
                                <label for="place_of_supply" class="form-label">Place of Supply *</label>
                                <select class="form-select" id="place_of_supply" name="place_of_supply" required>
                                    <option value="">Select State</option>
                                    <?php foreach ($indian_states as $state): ?>
                                        <option value="<?php echo htmlspecialchars($state); ?>">
                                            <?php echo htmlspecialchars($state); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Please select place of supply.</div>
                            </div>
                        </div>
                    </div>
