#!/usr/bin/env python3
"""
Simple HTTP server to serve the GST e-Invoice application files
This is for demonstration purposes only - PHP files won't execute
"""

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

PORT = 8000
DIRECTORY = Path(__file__).parent

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

def main():
    print("=" * 60)
    print("GST e-Invoice Application - File Server")
    print("=" * 60)
    print(f"Starting server on port {PORT}")
    print(f"Serving directory: {DIRECTORY}")
    print(f"Access the application at: http://localhost:{PORT}")
    print()
    print("NOTE: This is a static file server for demonstration.")
    print("PHP files will be displayed as text, not executed.")
    print()
    print("To run the actual PHP application:")
    print("1. Install PHP 7.4+ and MySQL 8.0+")
    print("2. Set up database using database/schema.sql")
    print("3. Configure database credentials in config/config.php")
    print("4. Run: php -S localhost:8000")
    print("5. Access: http://localhost:8000")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"Server running at http://localhost:{PORT}/")
            
            # Try to open browser
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("Opening browser...")
            except:
                print("Could not open browser automatically")
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")

if __name__ == "__main__":
    main()
