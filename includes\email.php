<?php
/**
 * Email Notification System
 * GST e-Invoice Application
 */

/**
 * Send email using PHP mail function
 */
function sendEmail($to, $subject, $message, $from_email = null, $from_name = null) {
    try {
        $from_email = $from_email ?? FROM_EMAIL;
        $from_name = $from_name ?? FROM_NAME;
        
        // Email headers
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . $from_name . ' <' . $from_email . '>',
            'Reply-To: ' . $from_email,
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // Send email
        $result = mail($to, $subject, $message, implode("\r\n", $headers));
        
        if ($result) {
            logSecurityEvent('email_sent', "Email sent to: {$to}, Subject: {$subject}");
            return true;
        } else {
            logSecurityEvent('email_failed', "Failed to send email to: {$to}, Subject: {$subject}");
            return false;
        }
        
    } catch (Exception $e) {
        error_log('Email sending error: ' . $e->getMessage());
        logSecurityEvent('email_error', "Email error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send password reset email
 */
function sendPasswordResetEmail($email, $reset_token, $user_name) {
    $reset_link = BASE_URL . "modules/auth/reset-password.php?token=" . $reset_token;
    
    $subject = "Password Reset Request - " . APP_NAME;
    
    $message = getEmailTemplate('password_reset', [
        'user_name' => $user_name,
        'reset_link' => $reset_link,
        'app_name' => APP_NAME,
        'expiry_time' => '1 hour'
    ]);
    
    return sendEmail($email, $subject, $message);
}

/**
 * Send invoice notification email
 */
function sendInvoiceNotificationEmail($invoice_id, $recipient_email, $invoice_number) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Get invoice details
        $stmt = $db->prepare("
            SELECT i.*, s.name as supplier_name, r.name as recipient_name
            FROM invoices i
            JOIN suppliers s ON i.supplier_id = s.id
            JOIN recipients r ON i.recipient_id = r.id
            WHERE i.id = ?
        ");
        $stmt->execute([$invoice_id]);
        $invoice = $stmt->fetch();
        
        if (!$invoice) {
            return false;
        }
        
        $subject = "Invoice Generated - " . $invoice_number;
        
        $message = getEmailTemplate('invoice_notification', [
            'recipient_name' => $invoice['recipient_name'],
            'supplier_name' => $invoice['supplier_name'],
            'invoice_number' => $invoice_number,
            'invoice_date' => date('d M Y', strtotime($invoice['date'])),
            'total_amount' => number_format($invoice['total_amount'], 2),
            'view_link' => BASE_URL . "modules/invoices/view.php?id=" . $invoice_id,
            'app_name' => APP_NAME
        ]);
        
        return sendEmail($recipient_email, $subject, $message);
        
    } catch (Exception $e) {
        error_log('Invoice notification error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Send user registration notification
 */
function sendUserRegistrationEmail($email, $name, $password) {
    $subject = "Welcome to " . APP_NAME;
    
    $message = getEmailTemplate('user_registration', [
        'user_name' => $name,
        'email' => $email,
        'password' => $password,
        'login_link' => BASE_URL . 'login.php',
        'app_name' => APP_NAME
    ]);
    
    return sendEmail($email, $subject, $message);
}

/**
 * Send security alert email
 */
function sendSecurityAlertEmail($user_email, $event_type, $details) {
    $subject = "Security Alert - " . APP_NAME;
    
    $message = getEmailTemplate('security_alert', [
        'event_type' => $event_type,
        'details' => $details,
        'timestamp' => date('d M Y H:i:s'),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
        'app_name' => APP_NAME
    ]);
    
    return sendEmail($user_email, $subject, $message);
}

/**
 * Get email template
 */
function getEmailTemplate($template_name, $variables = []) {
    $templates = [
        'password_reset' => '
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #007bff;">Password Reset Request</h2>
                    <p>Dear {user_name},</p>
                    <p>You have requested to reset your password for {app_name}.</p>
                    <p>Click the link below to reset your password:</p>
                    <p><a href="{reset_link}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
                    <p>This link will expire in {expiry_time}.</p>
                    <p>If you did not request this password reset, please ignore this email.</p>
                    <hr>
                    <p style="font-size: 12px; color: #666;">This is an automated email from {app_name}. Please do not reply.</p>
                </div>
            </body>
            </html>
        ',
        
        'invoice_notification' => '
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #28a745;">Invoice Generated</h2>
                    <p>Dear {recipient_name},</p>
                    <p>A new invoice has been generated for you by {supplier_name}.</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <h4>Invoice Details:</h4>
                        <p><strong>Invoice Number:</strong> {invoice_number}</p>
                        <p><strong>Date:</strong> {invoice_date}</p>
                        <p><strong>Amount:</strong> ₹{total_amount}</p>
                    </div>
                    <p><a href="{view_link}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Invoice</a></p>
                    <hr>
                    <p style="font-size: 12px; color: #666;">This is an automated email from {app_name}. Please do not reply.</p>
                </div>
            </body>
            </html>
        ',
        
        'user_registration' => '
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #007bff;">Welcome to {app_name}</h2>
                    <p>Dear {user_name},</p>
                    <p>Your account has been created successfully.</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <h4>Login Credentials:</h4>
                        <p><strong>Email:</strong> {email}</p>
                        <p><strong>Password:</strong> {password}</p>
                    </div>
                    <p><a href="{login_link}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login Now</a></p>
                    <p><strong>Important:</strong> Please change your password after first login for security.</p>
                    <hr>
                    <p style="font-size: 12px; color: #666;">This is an automated email from {app_name}. Please do not reply.</p>
                </div>
            </body>
            </html>
        ',
        
        'security_alert' => '
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #dc3545;">Security Alert</h2>
                    <p>A security event has been detected on your account.</p>
                    <div style="background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;">
                        <h4>Event Details:</h4>
                        <p><strong>Event Type:</strong> {event_type}</p>
                        <p><strong>Details:</strong> {details}</p>
                        <p><strong>Time:</strong> {timestamp}</p>
                        <p><strong>IP Address:</strong> {ip_address}</p>
                    </div>
                    <p>If this was not you, please contact support immediately.</p>
                    <hr>
                    <p style="font-size: 12px; color: #666;">This is an automated security alert from {app_name}. Please do not reply.</p>
                </div>
            </body>
            </html>
        '
    ];
    
    if (!isset($templates[$template_name])) {
        return false;
    }
    
    $template = $templates[$template_name];
    
    // Replace variables in template
    foreach ($variables as $key => $value) {
        $template = str_replace('{' . $key . '}', $value, $template);
    }
    
    return $template;
}

/**
 * Send bulk email notifications
 */
function sendBulkEmails($recipients, $subject, $message) {
    $success_count = 0;
    $failed_count = 0;
    
    foreach ($recipients as $recipient) {
        if (sendEmail($recipient, $subject, $message)) {
            $success_count++;
        } else {
            $failed_count++;
        }
        
        // Small delay to prevent overwhelming the mail server
        usleep(100000); // 0.1 second delay
    }
    
    logSecurityEvent('bulk_email', "Bulk email sent: {$success_count} success, {$failed_count} failed");
    
    return [
        'success' => $success_count,
        'failed' => $failed_count
    ];
}

/**
 * Queue email for later sending (basic implementation)
 */
function queueEmail($to, $subject, $message, $priority = 'normal') {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $stmt = $db->prepare("
            INSERT INTO email_queue (to_email, subject, message, priority, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$to, $subject, $message, $priority]);
        
        return true;
        
    } catch (Exception $e) {
        error_log('Email queue error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Process email queue (to be called by cron job)
 */
function processEmailQueue($limit = 10) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Get pending emails
        $stmt = $db->prepare("
            SELECT * FROM email_queue 
            WHERE status = 'pending' 
            ORDER BY priority DESC, created_at ASC 
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        $emails = $stmt->fetchAll();
        
        $processed = 0;
        
        foreach ($emails as $email) {
            if (sendEmail($email['to_email'], $email['subject'], $email['message'])) {
                // Mark as sent
                $update_stmt = $db->prepare("
                    UPDATE email_queue 
                    SET status = 'sent', sent_at = NOW() 
                    WHERE id = ?
                ");
                $update_stmt->execute([$email['id']]);
                $processed++;
            } else {
                // Mark as failed
                $update_stmt = $db->prepare("
                    UPDATE email_queue 
                    SET status = 'failed', attempts = attempts + 1 
                    WHERE id = ?
                ");
                $update_stmt->execute([$email['id']]);
            }
        }
        
        return $processed;
        
    } catch (Exception $e) {
        error_log('Email queue processing error: ' . $e->getMessage());
        return 0;
    }
}
?>
