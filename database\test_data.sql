-- Test Data for GST e-Invoice Application
-- This file contains sample data for testing the application

-- Insert test users (password is 'Test@123' for all users)
INSERT INTO users (name, email, password, role, status, created_at) VALUES
('System Administrator', '<EMAIL>', '$argon2id$v=19$m=65536,t=4,p=3$YWJjZGVmZ2hpams$K5k5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y', 'admin', 'active', NOW()),
('<PERSON>', '<EMAIL>', '$argon2id$v=19$m=65536,t=4,p=3$YWJjZGVmZ2hpams$K5k5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y', 'user', 'active', NOW()),
('<PERSON>', '<EMAIL>', '$argon2id$v=19$m=65536,t=4,p=3$YWJjZGVmZ2hpams$K5k5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y5Y', 'user', 'active', NOW());

-- Insert test suppliers
INSERT INTO suppliers (name, address, state, pincode, gstin, contact_person, email, phone, created_by, created_at) VALUES
('ABC Manufacturing Ltd', '123 Industrial Area, Sector 15\nGurgaon, Haryana', 'Haryana', '122001', '06ABCDE1234F1Z5', 'Rajesh Kumar', '<EMAIL>', '9876543210', 1, NOW()),
('XYZ Traders Pvt Ltd', '456 Commercial Complex\nBangalore, Karnataka', 'Karnataka', '560001', '29XYZAB5678C1Z9', 'Priya Sharma', '<EMAIL>', '9876543211', 1, NOW()),
('PQR Industries', '789 Business Park\nPune, Maharashtra', 'Maharashtra', '411001', '27PQRST9012D1Z3', 'Amit Patel', '<EMAIL>', '9876543212', 1, NOW()),
('LMN Services', '321 Tech Hub\nHyderabad, Telangana', 'Telangana', '500001', '36LMNOP3456E1Z7', 'Sunita Reddy', '<EMAIL>', '9876543213', 1, NOW()),
('DEF Enterprises', '654 Trade Center\nChennai, Tamil Nadu', 'Tamil Nadu', '600001', '33DEFGH7890F1Z1', 'Karthik Raj', '<EMAIL>', '9876543214', 1, NOW());

-- Insert test recipients
INSERT INTO recipients (name, address, state, pincode, gstin, contact_person, email, phone, created_by, created_at) VALUES
('Global Tech Solutions', '100 IT Park, Phase 2\nNoida, Uttar Pradesh', 'Uttar Pradesh', '201301', '09GLBTE1234G1Z8', 'Vikram Singh', '<EMAIL>', '9876543220', 1, NOW()),
('Modern Retail Chain', '200 Shopping Mall\nMumbai, Maharashtra', 'Maharashtra', '400001', '27MODRT5678H1Z2', 'Neha Gupta', '<EMAIL>', '9876543221', 1, NOW()),
('Smart Electronics', '300 Electronics Market\nDelhi, Delhi', 'Delhi', '110001', '07SMTEL9012I1Z6', 'Rohit Sharma', '<EMAIL>', '9876543222', 1, NOW()),
('Future Innovations', '400 Research Center\nKolkata, West Bengal', 'West Bengal', '700001', '19FUTIV3456J1Z0', 'Anita Das', '<EMAIL>', '9876543223', 1, NOW()),
('Digital Solutions Hub', '500 Software Park\nAhmedabad, Gujarat', 'Gujarat', '380001', '24DIGSL7890K1Z4', 'Ravi Patel', '<EMAIL>', '**********', 1, NOW());

-- Insert test products
INSERT INTO products (name, description, hsn_sac, unit, gst_rate, created_by, created_at) VALUES
('Laptop Computer', 'High-performance business laptop', '********', 'Nos', 18.00, 1, NOW()),
('Office Chair', 'Ergonomic office chair with lumbar support', '94013000', 'Nos', 18.00, 1, NOW()),
('Printer Paper', 'A4 size white printing paper', '48025590', 'Ream', 12.00, 1, NOW()),
('Software License', 'Annual software license subscription', '998313', 'Nos', 18.00, 1, NOW()),
('Mobile Phone', 'Smartphone with advanced features', '85171200', 'Nos', 18.00, 1, NOW()),
('Desk Lamp', 'LED desk lamp with adjustable brightness', '94051000', 'Nos', 18.00, 1, NOW()),
('External Hard Drive', '1TB external storage device', '84717050', 'Nos', 18.00, 1, NOW()),
('Wireless Mouse', 'Optical wireless computer mouse', '84716070', 'Nos', 18.00, 1, NOW()),
('Keyboard', 'Mechanical keyboard for computers', '84716060', 'Nos', 18.00, 1, NOW()),
('Monitor', '24-inch LED computer monitor', '85285200', 'Nos', 18.00, 1, NOW());

-- Insert test invoices
INSERT INTO invoices (invoice_number, supplier_id, recipient_id, date, due_date, place_of_supply, subtotal, cgst_amount, sgst_amount, igst_amount, total_amount, status, user_id, created_at) VALUES
('INV-2024-001', 1, 1, '2024-01-15', '2024-02-14', 'Uttar Pradesh', 50000.00, 0.00, 0.00, 9000.00, 59000.00, 'sent', 2, NOW()),
('INV-2024-002', 2, 2, '2024-01-16', '2024-02-15', 'Maharashtra', 75000.00, 6750.00, 6750.00, 0.00, 88500.00, 'paid', 2, NOW()),
('INV-2024-003', 3, 3, '2024-01-17', '2024-02-16', 'Delhi', 30000.00, 0.00, 0.00, 5400.00, 35400.00, 'draft', 3, NOW()),
('INV-2024-004', 4, 4, '2024-01-18', '2024-02-17', 'West Bengal', 45000.00, 0.00, 0.00, 8100.00, 53100.00, 'sent', 2, NOW()),
('INV-2024-005', 5, 5, '2024-01-19', '2024-02-18', 'Gujarat', 60000.00, 0.00, 0.00, 10800.00, 70800.00, 'paid', 3, NOW());

-- Insert test invoice items
INSERT INTO invoice_items (invoice_id, product_id, description, hsn_sac, quantity, unit, rate, amount, gst_rate, cgst_amount, sgst_amount, igst_amount) VALUES
-- Items for Invoice 1 (INV-2024-001)
(1, 1, 'Laptop Computer', '********', 2.00, 'Nos', 25000.00, 50000.00, 18.00, 0.00, 0.00, 9000.00),

-- Items for Invoice 2 (INV-2024-002)
(2, 2, 'Office Chair', '94013000', 5.00, 'Nos', 15000.00, 75000.00, 18.00, 6750.00, 6750.00, 0.00),

-- Items for Invoice 3 (INV-2024-003)
(3, 3, 'Printer Paper', '48025590', 100.00, 'Ream', 300.00, 30000.00, 18.00, 0.00, 0.00, 5400.00),

-- Items for Invoice 4 (INV-2024-004)
(4, 4, 'Software License', '998313', 3.00, 'Nos', 15000.00, 45000.00, 18.00, 0.00, 0.00, 8100.00),

-- Items for Invoice 5 (INV-2024-005)
(5, 5, 'Mobile Phone', '85171200', 4.00, 'Nos', 15000.00, 60000.00, 18.00, 0.00, 0.00, 10800.00);

-- Insert audit log entries
INSERT INTO audit_log (table_name, record_id, action, old_values, new_values, user_id, ip_address, created_at) VALUES
('suppliers', 1, 'INSERT', NULL, '{"name":"ABC Manufacturing Ltd","gstin":"06ABCDE1234F1Z5"}', 1, '127.0.0.1', NOW()),
('recipients', 1, 'INSERT', NULL, '{"name":"Global Tech Solutions","gstin":"09GLBTE1234G1Z8"}', 1, '127.0.0.1', NOW()),
('products', 1, 'INSERT', NULL, '{"name":"Laptop Computer","hsn_sac":"********"}', 1, '127.0.0.1', NOW()),
('invoices', 1, 'INSERT', NULL, '{"invoice_number":"INV-2024-001","total_amount":"59000.00"}', 2, '127.0.0.1', NOW()),
('invoices', 2, 'INSERT', NULL, '{"invoice_number":"INV-2024-002","total_amount":"88500.00"}', 2, '127.0.0.1', NOW());

-- Insert security log entries
INSERT INTO security_log (event_type, description, user_id, ip_address, user_agent, created_at) VALUES
('login_success', 'User logged in successfully', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW()),
('login_success', 'User logged in successfully', 2, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW()),
('invoice_created', 'New invoice created: INV-2024-001', 2, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW()),
('invoice_created', 'New invoice created: INV-2024-002', 2, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW()),
('data_export', 'Invoice data exported to CSV', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW());

-- Additional test data for comprehensive testing

-- More suppliers for testing pagination and search
INSERT INTO suppliers (name, address, state, pincode, gstin, contact_person, email, phone, created_by, created_at) VALUES
('Tech Innovations Pvt Ltd', '111 Innovation Hub\nBangalore, Karnataka', 'Karnataka', '560002', '29TECIN1111A1Z1', 'Arjun Reddy', '<EMAIL>', '9876543230', 1, NOW()),
('Green Energy Solutions', '222 Eco Park\nPune, Maharashtra', 'Maharashtra', '411002', '27GRENE2222B1Z2', 'Meera Joshi', '<EMAIL>', '9876543231', 1, NOW()),
('Digital Marketing Hub', '333 Media Center\nGurgaon, Haryana', 'Haryana', '122002', '06DIGMA3333C1Z3', 'Sanjay Gupta', '<EMAIL>', '9876543232', 1, NOW()),
('Smart Logistics', '444 Transport Nagar\nNagpur, Maharashtra', 'Maharashtra', '440001', '27SMLOG4444D1Z4', 'Pooja Sharma', '<EMAIL>', '9876543233', 1, NOW()),
('Cloud Services Inc', '555 Data Center\nChennai, Tamil Nadu', 'Tamil Nadu', '600002', '33CLSER5555E1Z5', 'Ramesh Kumar', '<EMAIL>', '9876543234', 1, NOW());

-- More recipients for testing
INSERT INTO recipients (name, address, state, pincode, gstin, contact_person, email, phone, created_by, created_at) VALUES
('Retail Excellence', '600 Shopping District\nJaipur, Rajasthan', 'Rajasthan', '302001', '08RETEX6666F1Z6', 'Kavita Agarwal', '<EMAIL>', '9876543240', 1, NOW()),
('Manufacturing Pro', '700 Industrial Zone\nLudhiana, Punjab', 'Punjab', '141001', '03MANPR7777G1Z7', 'Harpreet Singh', '<EMAIL>', '9876543241', 1, NOW()),
('Service Masters', '800 Business Complex\nKochi, Kerala', 'Kerala', '682001', '32SERMA8888H1Z8', 'Lakshmi Nair', '<EMAIL>', '9876543242', 1, NOW()),
('Tech Distributors', '900 Technology Park\nVisakhapatnam, Andhra Pradesh', 'Andhra Pradesh', '530001', '37TECDI9999I1Z9', 'Venkat Rao', '<EMAIL>', '9876543243', 1, NOW()),
('Export House', '1000 Export Zone\nKandla, Gujarat', 'Gujarat', '370201', '24EXPHO0000J1Z0', 'Nisha Patel', '<EMAIL>', '**********', 1, NOW());

-- More products for testing
INSERT INTO products (name, description, hsn_sac, unit, gst_rate, created_by, created_at) VALUES
('Tablet Device', '10-inch Android tablet', '********', 'Nos', 18.00, 1, NOW()),
('Webcam', 'HD webcam for video conferencing', '********', 'Nos', 18.00, 1, NOW()),
('Headphones', 'Noise-cancelling headphones', '********', 'Nos', 18.00, 1, NOW()),
('Power Bank', 'Portable power bank 10000mAh', '********', 'Nos', 18.00, 1, NOW()),
('USB Cable', 'USB Type-C charging cable', '********', 'Nos', 18.00, 1, NOW()),
('Screen Protector', 'Tempered glass screen protector', '********', 'Nos', 18.00, 1, NOW()),
('Phone Case', 'Protective phone case', '********', 'Nos', 18.00, 1, NOW()),
('Bluetooth Speaker', 'Portable Bluetooth speaker', '********', 'Nos', 18.00, 1, NOW()),
('Memory Card', '64GB microSD memory card', '********', 'Nos', 18.00, 1, NOW()),
('Car Charger', 'Dual USB car charger', '********', 'Nos', 18.00, 1, NOW());

-- Update system settings for testing
UPDATE system_settings SET setting_value = 'Test Company Pvt Ltd' WHERE setting_key = 'company_name';
UPDATE system_settings SET setting_value = '123 Test Address, Test City, Test State - 123456' WHERE setting_key = 'company_address';
UPDATE system_settings SET setting_value = '29TESTC1234T1Z5' WHERE setting_key = 'company_gstin';
UPDATE system_settings SET setting_value = 'TST' WHERE setting_key = 'invoice_prefix';
UPDATE system_settings SET setting_value = '1001' WHERE setting_key = 'invoice_start_number';

-- Note: The password hashes above are examples. In a real implementation, 
-- you would generate proper Argon2ID hashes for the actual passwords.
-- The actual hash for 'Test@123' would be generated using PHP's password_hash() function.
