<?php
/**
 * Logout Script
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Log logout action if user is logged in
if (isLoggedIn()) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $stmt = $db->prepare("
            INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
            VALUES (?, 'users', ?, 'LOGOUT', ?, ?, ?)
        ");
        $stmt->execute([
            $_SESSION['user_id'], 
            $_SESSION['user_id'], 
            json_encode(['logout_time' => date('Y-m-d H:i:s')]),
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        error_log('Logout logging error: ' . $e->getMessage());
    }
}

// Clear all session data
session_unset();
session_destroy();

// Start new session for flash message
session_start();
$_SESSION['success_message'] = 'You have been logged out successfully.';

// Redirect to login page
header('Location: ../../login.php');
exit();
?>
