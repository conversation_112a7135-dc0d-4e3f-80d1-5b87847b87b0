# GST e-Invoice Web Application

A comprehensive web-based GST e-Invoice management system for small to mid-sized businesses in India, designed to replace desktop tools like NIC-GePP with a modern, browser-based solution.

## Features

### Core Functionality
- **Multi-user Support**: Role-based access control (Admin/Standard users)
- **Master Data Management**: Suppliers, Recipients, and Products with GSTIN validation
- **Invoice Creation**: Dynamic invoice generation with real-time tax calculations
- **NIC Schema Compliance**: Ver 1.1 JSON output for GST e-invoice submission
- **Comprehensive Reporting**: Dashboard, analytics, and export capabilities
- **Print & QR Codes**: Professional invoice printing with verification QR codes

### Security Features
- **Authentication**: Secure login with session management and password recovery
- **CSRF Protection**: Token-based protection against cross-site request forgery
- **Input Validation**: Comprehensive server-side validation for all inputs
- **Audit Trail**: Complete logging of all user actions and system events
- **Rate Limiting**: Protection against brute force attacks
- **File Upload Security**: Safe handling of CSV imports with validation

### GST Compliance
- **GSTIN Validation**: Real-time validation of supplier and recipient GSTIN
- **HSN/SAC Codes**: Validation and management of product classification codes
- **Tax Calculations**: Automatic CGST/SGST/IGST calculation based on state mapping
- **State Code Mapping**: Complete Indian state code mapping for NIC compliance
- **Invoice Numbering**: Configurable invoice number generation with prefixes

## Technology Stack

- **Backend**: PHP 7.4+ with PDO for database operations
- **Frontend**: Bootstrap 5, jQuery, Font Awesome icons
- **Database**: MySQL 8.0+ with comprehensive schema and indexes
- **Security**: Argon2ID password hashing, CSRF tokens, input sanitization
- **Charts**: Chart.js for reporting and analytics
- **QR Codes**: QR code generation for invoice verification

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 8.0 or higher
- Web server (Apache/Nginx)
- Composer (optional, for dependencies)

### Setup Instructions

1. **Clone/Download the Application**
   ```bash
   git clone <repository-url>
   cd gst-einvoice-app
   ```

2. **Database Setup**
   - Create a new MySQL database
   - Import the schema: `mysql -u username -p database_name < database/schema.sql`
   - Update database credentials in `config/config.php`

3. **Configuration**
   - Copy `config/config.example.php` to `config/config.php`
   - Update database credentials and other settings
   - Set appropriate file permissions for uploads directory

4. **Web Server Configuration**
   - Point document root to the application directory
   - Ensure mod_rewrite is enabled (for Apache)
   - Configure SSL certificate for production use

5. **Initial Setup**
   - Access the application in your browser
   - Default admin credentials: <EMAIL> / Admin@123
   - Change default password immediately after first login

## Directory Structure

```
gst-einvoice-app/
├── assets/                 # CSS, JS, and image files
│   ├── css/
│   ├── js/
│   └── images/
├── config/                 # Configuration files
│   └── config.php
├── database/               # Database schema and migrations
│   └── schema.sql
├── docs/                   # Documentation
│   ├── README.md
│   ├── USER_GUIDE.md
│   └── API_DOCUMENTATION.md
├── includes/               # Common PHP includes
│   ├── header.php
│   ├── footer.php
│   ├── functions.php
│   └── security.php
├── modules/                # Application modules
│   ├── auth/              # Authentication
│   ├── dashboard/         # Dashboard
│   ├── invoices/          # Invoice management
│   ├── masters/           # Master data (suppliers, recipients, products)
│   ├── reports/           # Reporting and analytics
│   └── users/             # User management
├── templates/             # CSV import templates
├── uploads/               # File upload directory
└── index.php             # Application entry point
```

## User Guide

### Getting Started

1. **Login**: Use your credentials to access the system
2. **Setup Masters**: Configure suppliers, recipients, and products
3. **Create Invoices**: Generate GST-compliant invoices
4. **Generate Reports**: Access comprehensive reporting features
5. **Export Data**: Download reports and invoice data

### Master Data Management

#### Suppliers
- Add supplier details with GSTIN validation
- Import bulk suppliers via CSV
- Manage supplier addresses and contact information

#### Recipients
- Maintain customer/recipient database
- GSTIN validation for B2B transactions
- State-wise organization for tax calculations

#### Products
- HSN/SAC code validation
- Tax rate configuration (0%, 5%, 12%, 18%, 28%)
- Unit of measurement management

### Invoice Creation

1. **Select Supplier**: Choose from configured suppliers
2. **Select Recipient**: Choose customer/recipient
3. **Add Items**: Add products with quantities and rates
4. **Review Calculations**: Verify tax calculations (CGST/SGST/IGST)
5. **Generate Invoice**: Create invoice with unique number
6. **Export JSON**: Generate NIC-compliant JSON for e-invoice submission

### Reporting

- **Dashboard**: Overview of invoice statistics and trends
- **Invoice Reports**: Detailed invoice listings with filters
- **Tax Reports**: GST summary reports by period
- **Export Options**: CSV, PDF export capabilities

## API Documentation

### Authentication Endpoints

- `POST /modules/auth/login.php` - User login
- `POST /modules/auth/logout.php` - User logout
- `POST /modules/auth/forgot_password.php` - Password recovery

### Invoice Endpoints

- `GET /modules/invoices/list.php` - List invoices
- `POST /modules/invoices/create.php` - Create new invoice
- `GET /modules/invoices/view.php?id={id}` - View invoice details
- `GET /modules/invoices/generate_json.php?id={id}` - Generate NIC JSON

### Master Data Endpoints

- `GET /modules/masters/suppliers.php` - Manage suppliers
- `GET /modules/masters/recipients.php` - Manage recipients
- `GET /modules/masters/products.php` - Manage products

## Security Considerations

### Production Deployment

1. **SSL Certificate**: Always use HTTPS in production
2. **Database Security**: Use strong passwords and restrict access
3. **File Permissions**: Set appropriate permissions (644 for files, 755 for directories)
4. **Regular Updates**: Keep PHP and MySQL updated
5. **Backup Strategy**: Implement regular database backups
6. **Monitoring**: Set up logging and monitoring for security events

### Security Features

- **Password Policy**: Enforced strong password requirements
- **Session Security**: Secure session handling with timeouts
- **Input Validation**: All inputs validated and sanitized
- **SQL Injection Protection**: Prepared statements used throughout
- **XSS Protection**: Output encoding and CSP headers
- **CSRF Protection**: Token-based protection for all forms

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in config.php
   - Verify MySQL service is running
   - Check database user permissions

2. **File Upload Issues**
   - Verify uploads directory permissions (755)
   - Check PHP upload_max_filesize setting
   - Ensure web server has write access

3. **Session Issues**
   - Check PHP session configuration
   - Verify session directory permissions
   - Clear browser cookies and cache

4. **GSTIN Validation Errors**
   - Ensure GSTIN format is correct (15 characters)
   - Check for special characters or spaces
   - Verify state code matches GSTIN prefix

### Error Logs

- Application logs: Check web server error logs
- Database logs: Check MySQL error logs
- Security logs: Review security_log table in database

## Support

For technical support or questions:
- Check documentation in `/docs` directory
- Review error logs for specific issues
- Contact system administrator for access issues

## License

This application is developed for internal business use. All rights reserved.

## Version History

- **v1.0.0** - Initial release with core functionality
- **v1.1.0** - Added reporting and analytics features
- **v1.2.0** - Enhanced security and validation features

---

**Note**: This application is designed specifically for Indian GST compliance and follows NIC e-invoice schema Ver 1.1 specifications.
