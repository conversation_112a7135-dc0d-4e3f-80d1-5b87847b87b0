/**
 * Main JavaScript file for GST e-Invoice Application
 */

$(document).ready(function() {
    // Initialize DataTables
    if ($('.data-table').length) {
        $('.data-table').DataTable({
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            language: {
                search: "Search:",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            }
        });
    }
    
    // GSTIN Validation
    function validateGSTIN(gstin) {
        const gstinRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
        return gstinRegex.test(gstin);
    }
    
    // HSN/SAC Code Validation
    function validateHSNSAC(code) {
        const hsnRegex = /^[0-9]{4,8}$/;
        return hsnRegex.test(code);
    }
    
    // Real-time GSTIN validation
    $(document).on('input', '.gstin-input', function() {
        const gstin = $(this).val().toUpperCase();
        $(this).val(gstin);
        
        if (gstin.length === 15) {
            if (validateGSTIN(gstin)) {
                $(this).removeClass('is-invalid').addClass('is-valid');
                $(this).siblings('.invalid-feedback').hide();
            } else {
                $(this).removeClass('is-valid').addClass('is-invalid');
                $(this).siblings('.invalid-feedback').text('Invalid GSTIN format').show();
            }
        } else if (gstin.length > 0) {
            $(this).removeClass('is-valid is-invalid');
        }
    });
    
    // Real-time HSN/SAC validation
    $(document).on('input', '.hsn-input', function() {
        const hsn = $(this).val();
        
        if (hsn.length >= 4) {
            if (validateHSNSAC(hsn)) {
                $(this).removeClass('is-invalid').addClass('is-valid');
                $(this).siblings('.invalid-feedback').hide();
            } else {
                $(this).removeClass('is-valid').addClass('is-invalid');
                $(this).siblings('.invalid-feedback').text('Invalid HSN/SAC code format').show();
            }
        } else if (hsn.length > 0) {
            $(this).removeClass('is-valid is-invalid');
        }
    });
    
    // Form validation
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });
    
    // Auto-uppercase for specific fields
    $(document).on('input', '.uppercase', function() {
        $(this).val($(this).val().toUpperCase());
    });
    
    // Number formatting
    $(document).on('input', '.currency-input', function() {
        let value = $(this).val().replace(/[^\d.]/g, '');
        if (value) {
            $(this).val(parseFloat(value).toFixed(2));
        }
    });
    
    // Confirm dialogs
    $(document).on('click', '.confirm-delete', function(e) {
        if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Loading states for buttons
    $(document).on('click', '.btn-loading', function() {
        const $btn = $(this);
        const originalText = $btn.html();
        
        $btn.html('<span class="loading-spinner me-2"></span>Processing...').prop('disabled', true);
        
        // Re-enable after 5 seconds (fallback)
        setTimeout(function() {
            $btn.html(originalText).prop('disabled', false);
        }, 5000);
    });
    
    // Auto-save drafts (for invoice form)
    let autoSaveTimer;
    $(document).on('input', '.auto-save', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            saveDraft();
        }, 2000);
    });
    
    // Copy to clipboard functionality
    $(document).on('click', '.copy-to-clipboard', function() {
        const text = $(this).data('copy-text') || $(this).text();
        navigator.clipboard.writeText(text).then(function() {
            showToast('Copied to clipboard!', 'success');
        });
    });
    
    // Show toast notifications
    function showToast(message, type = 'info') {
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        if (!$('#toast-container').length) {
            $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
        }
        
        const $toast = $(toastHtml);
        $('#toast-container').append($toast);
        
        const toast = new bootstrap.Toast($toast[0]);
        toast.show();
        
        $toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }
    
    // Export functionality
    $(document).on('click', '.export-btn', function() {
        const format = $(this).data('format');
        const url = $(this).data('url');
        
        if (url) {
            window.open(url + '?format=' + format, '_blank');
        }
    });
    
    // Print functionality
    $(document).on('click', '.print-btn', function() {
        const printContent = $(this).data('print-target');
        if (printContent) {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>Print</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                        <link href="${window.location.origin}/assets/css/style.css" rel="stylesheet">
                    </head>
                    <body>
                        ${$(printContent).html()}
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    });
});

// Global functions
function saveDraft() {
    // Implementation for auto-saving invoice drafts
    console.log('Auto-saving draft...');
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
    }).format(amount);
}

function formatNumber(number, decimals = 2) {
    return parseFloat(number).toFixed(decimals);
}

// AJAX error handling
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    console.error('AJAX Error:', thrownError);
    showToast('An error occurred. Please try again.', 'danger');
});

// Session timeout warning
let sessionWarningShown = false;
setInterval(function() {
    if (!sessionWarningShown) {
        // Check session status via AJAX
        $.get('includes/check_session.php', function(data) {
            if (data.warning && !sessionWarningShown) {
                sessionWarningShown = true;
                if (confirm('Your session will expire in 5 minutes. Do you want to extend it?')) {
                    $.post('includes/extend_session.php', function() {
                        sessionWarningShown = false;
                    });
                }
            }
        });
    }
}, 300000); // Check every 5 minutes
