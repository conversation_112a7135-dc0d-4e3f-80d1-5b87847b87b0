<?php
/**
 * Supplier Master Management
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login
requireLogin();

$page_title = 'Supplier Master';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!validateCSRFToken($csrf_token)) {
            $_SESSION['error_message'] = 'Invalid request. Please try again.';
        } else {
            switch ($action) {
                case 'create':
                    $name = sanitizeInput($_POST['name'] ?? '');
                    $address = sanitizeInput($_POST['address'] ?? '');
                    $state = sanitizeInput($_POST['state'] ?? '');
                    $gstin = strtoupper(sanitizeInput($_POST['gstin'] ?? ''));
                    $contact_person = sanitizeInput($_POST['contact_person'] ?? '');
                    $email = sanitizeInput($_POST['email'] ?? '');
                    $phone = sanitizeInput($_POST['phone'] ?? '');
                    
                    if (empty($name) || empty($state)) {
                        $_SESSION['error_message'] = 'Name and state are required.';
                    } elseif (!empty($gstin) && !preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $gstin)) {
                        $_SESSION['error_message'] = 'Invalid GSTIN format.';
                    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $_SESSION['error_message'] = 'Invalid email address.';
                    } else {
                        // Check if GSTIN already exists
                        if (!empty($gstin)) {
                            $stmt = $db->prepare("SELECT id FROM suppliers WHERE gstin = ?");
                            $stmt->execute([$gstin]);
                            
                            if ($stmt->fetch()) {
                                $_SESSION['error_message'] = 'GSTIN already exists.';
                                break;
                            }
                        }
                        
                        $stmt = $db->prepare("
                            INSERT INTO suppliers (name, address, state, gstin, contact_person, email, phone, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        
                        if ($stmt->execute([$name, $address, $state, $gstin, $contact_person, $email, $phone, $_SESSION['user_id']])) {
                            $_SESSION['success_message'] = 'Supplier created successfully.';
                            
                            // Log supplier creation
                            $supplier_id = $db->lastInsertId();
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                VALUES (?, 'suppliers', ?, 'INSERT', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $supplier_id, 
                                json_encode(['name' => $name, 'gstin' => $gstin]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to create supplier.';
                        }
                    }
                    break;
                    
                case 'update':
                    $supplier_id = (int)($_POST['supplier_id'] ?? 0);
                    $name = sanitizeInput($_POST['name'] ?? '');
                    $address = sanitizeInput($_POST['address'] ?? '');
                    $state = sanitizeInput($_POST['state'] ?? '');
                    $gstin = strtoupper(sanitizeInput($_POST['gstin'] ?? ''));
                    $contact_person = sanitizeInput($_POST['contact_person'] ?? '');
                    $email = sanitizeInput($_POST['email'] ?? '');
                    $phone = sanitizeInput($_POST['phone'] ?? '');
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    if (empty($name) || empty($state)) {
                        $_SESSION['error_message'] = 'Name and state are required.';
                    } elseif (!empty($gstin) && !preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $gstin)) {
                        $_SESSION['error_message'] = 'Invalid GSTIN format.';
                    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $_SESSION['error_message'] = 'Invalid email address.';
                    } else {
                        // Check if GSTIN exists for other suppliers
                        if (!empty($gstin)) {
                            $stmt = $db->prepare("SELECT id FROM suppliers WHERE gstin = ? AND id != ?");
                            $stmt->execute([$gstin, $supplier_id]);
                            
                            if ($stmt->fetch()) {
                                $_SESSION['error_message'] = 'GSTIN already exists.';
                                break;
                            }
                        }
                        
                        $stmt = $db->prepare("
                            UPDATE suppliers 
                            SET name = ?, address = ?, state = ?, gstin = ?, contact_person = ?, email = ?, phone = ?, is_active = ? 
                            WHERE id = ?
                        ");
                        
                        if ($stmt->execute([$name, $address, $state, $gstin, $contact_person, $email, $phone, $is_active, $supplier_id])) {
                            $_SESSION['success_message'] = 'Supplier updated successfully.';
                            
                            // Log supplier update
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                VALUES (?, 'suppliers', ?, 'UPDATE', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $supplier_id, 
                                json_encode(['name' => $name, 'gstin' => $gstin, 'is_active' => $is_active]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to update supplier.';
                        }
                    }
                    break;
                    
                case 'delete':
                    $supplier_id = (int)($_POST['supplier_id'] ?? 0);
                    
                    // Check if supplier has invoices
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoices WHERE supplier_id = ?");
                    $stmt->execute([$supplier_id]);
                    $invoice_count = $stmt->fetch()['count'];
                    
                    if ($invoice_count > 0) {
                        $_SESSION['error_message'] = 'Cannot delete supplier with existing invoices. Deactivate instead.';
                    } else {
                        $stmt = $db->prepare("DELETE FROM suppliers WHERE id = ?");
                        
                        if ($stmt->execute([$supplier_id])) {
                            $_SESSION['success_message'] = 'Supplier deleted successfully.';
                            
                            // Log supplier deletion
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, old_values, ip_address, user_agent) 
                                VALUES (?, 'suppliers', ?, 'DELETE', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $supplier_id, 
                                json_encode(['deleted_by' => $_SESSION['user_name']]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to delete supplier.';
                        }
                    }
                    break;
            }
        }
        
        header('Location: suppliers.php');
        exit();
    }
    
    // Handle CSV import
    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
        $csv_file = $_FILES['csv_file']['tmp_name'];
        $imported = 0;
        $errors = [];
        
        if (($handle = fopen($csv_file, 'r')) !== FALSE) {
            $header = fgetcsv($handle); // Skip header row
            
            while (($data = fgetcsv($handle)) !== FALSE) {
                if (count($data) >= 4) { // Minimum required fields
                    $name = sanitizeInput($data[0] ?? '');
                    $address = sanitizeInput($data[1] ?? '');
                    $state = sanitizeInput($data[2] ?? '');
                    $gstin = strtoupper(sanitizeInput($data[3] ?? ''));
                    $contact_person = sanitizeInput($data[4] ?? '');
                    $email = sanitizeInput($data[5] ?? '');
                    $phone = sanitizeInput($data[6] ?? '');
                    
                    if (!empty($name) && !empty($state)) {
                        // Validate GSTIN if provided
                        if (!empty($gstin) && !preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $gstin)) {
                            $errors[] = "Invalid GSTIN for $name: $gstin";
                            continue;
                        }
                        
                        // Check if GSTIN already exists
                        if (!empty($gstin)) {
                            $stmt = $db->prepare("SELECT id FROM suppliers WHERE gstin = ?");
                            $stmt->execute([$gstin]);
                            
                            if ($stmt->fetch()) {
                                $errors[] = "GSTIN already exists for $name: $gstin";
                                continue;
                            }
                        }
                        
                        $stmt = $db->prepare("
                            INSERT INTO suppliers (name, address, state, gstin, contact_person, email, phone, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ");
                        
                        if ($stmt->execute([$name, $address, $state, $gstin, $contact_person, $email, $phone, $_SESSION['user_id']])) {
                            $imported++;
                        } else {
                            $errors[] = "Failed to import $name";
                        }
                    }
                }
            }
            fclose($handle);
        }
        
        if ($imported > 0) {
            $_SESSION['success_message'] = "$imported suppliers imported successfully.";
        }
        
        if (!empty($errors)) {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
        
        header('Location: suppliers.php');
        exit();
    }
    
    // Get all suppliers with filtering
    $search = $_GET['search'] ?? '';
    $state_filter = $_GET['state'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(s.name LIKE ? OR s.gstin LIKE ? OR s.contact_person LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($state_filter)) {
        $where_conditions[] = "s.state = ?";
        $params[] = $state_filter;
    }
    
    if ($status_filter !== '') {
        $where_conditions[] = "s.is_active = ?";
        $params[] = (int)$status_filter;
    }
    
    // Add user restriction for non-admin users
    if (!isAdmin()) {
        $where_conditions[] = "s.created_by = ?";
        $params[] = $_SESSION['user_id'];
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $stmt = $db->prepare("
        SELECT s.*, 
               u.name as created_by_name,
               COUNT(i.id) as invoice_count
        FROM suppliers s
        LEFT JOIN users u ON s.created_by = u.id
        LEFT JOIN invoices i ON s.id = i.supplier_id
        $where_clause
        GROUP BY s.id
        ORDER BY s.created_at DESC
    ");
    $stmt->execute($params);
    $suppliers = $stmt->fetchAll();
    
    // Get unique states for filter
    $stmt = $db->prepare("SELECT DISTINCT state FROM suppliers WHERE state IS NOT NULL AND state != '' ORDER BY state");
    $stmt->execute();
    $states = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch (Exception $e) {
    error_log('Supplier management error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while loading suppliers.';
    $suppliers = [];
    $states = [];
}

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-building me-2"></i>
                Supplier Master
            </h2>
            <div>
                <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#importModal">
                    <i class="fas fa-upload me-2"></i>
                    Import CSV
                </button>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSupplierModal">
                    <i class="fas fa-plus me-2"></i>
                    Add Supplier
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="Name, GSTIN, or Contact Person">
                    </div>

                    <div class="col-md-3">
                        <label for="state" class="form-label">State</label>
                        <select class="form-select" id="state" name="state">
                            <option value="">All States</option>
                            <?php foreach ($states as $state): ?>
                                <option value="<?php echo htmlspecialchars($state); ?>"
                                        <?php echo $state_filter === $state ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($state); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>Active</option>
                            <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Suppliers Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>GSTIN</th>
                                <th>State</th>
                                <th>Contact Person</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Status</th>
                                <th>Invoices</th>
                                <?php if (isAdmin()): ?>
                                    <th>Created By</th>
                                <?php endif; ?>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($suppliers as $supplier): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($supplier['name']); ?></strong>
                                        <?php if (!empty($supplier['address'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($supplier['address']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($supplier['gstin'])): ?>
                                            <code><?php echo htmlspecialchars($supplier['gstin']); ?></code>
                                        <?php else: ?>
                                            <span class="text-muted">Not provided</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($supplier['state']); ?></td>
                                    <td><?php echo htmlspecialchars($supplier['contact_person']); ?></td>
                                    <td>
                                        <?php if (!empty($supplier['email'])): ?>
                                            <a href="mailto:<?php echo htmlspecialchars($supplier['email']); ?>">
                                                <?php echo htmlspecialchars($supplier['email']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($supplier['phone'])): ?>
                                            <a href="tel:<?php echo htmlspecialchars($supplier['phone']); ?>">
                                                <?php echo htmlspecialchars($supplier['phone']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($supplier['is_active']): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($supplier['invoice_count'] > 0): ?>
                                            <span class="badge bg-info"><?php echo $supplier['invoice_count']; ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">None</span>
                                        <?php endif; ?>
                                    </td>
                                    <?php if (isAdmin()): ?>
                                        <td><?php echo htmlspecialchars($supplier['created_by_name']); ?></td>
                                    <?php endif; ?>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary"
                                                    onclick="editSupplier(<?php echo htmlspecialchars(json_encode($supplier)); ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="deleteSupplier(<?php echo $supplier['id']; ?>, '<?php echo htmlspecialchars($supplier['name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <?php if (empty($suppliers)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No suppliers found</h5>
                        <p class="text-muted">Add your first supplier to get started.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSupplierModal">
                            <i class="fas fa-plus me-2"></i>
                            Add Supplier
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Create Supplier Modal -->
<div class="modal fade" id="createSupplierModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-building me-2"></i>
                    Add New Supplier
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="create">

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="create_name" class="form-label">Supplier Name *</label>
                            <input type="text" class="form-control" id="create_name" name="name" required>
                            <div class="invalid-feedback">Please enter supplier name.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="create_gstin" class="form-label">GSTIN</label>
                            <input type="text" class="form-control gstin-input" id="create_gstin" name="gstin"
                                   pattern="[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}"
                                   placeholder="22AAAAA0000A1Z5">
                            <div class="invalid-feedback">Please enter valid GSTIN format.</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="create_address" class="form-label">Address</label>
                        <textarea class="form-control" id="create_address" name="address" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="create_state" class="form-label">State *</label>
                            <select class="form-select" id="create_state" name="state" required>
                                <option value="">Select State</option>
                                <option value="Andhra Pradesh">Andhra Pradesh</option>
                                <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                <option value="Assam">Assam</option>
                                <option value="Bihar">Bihar</option>
                                <option value="Chhattisgarh">Chhattisgarh</option>
                                <option value="Goa">Goa</option>
                                <option value="Gujarat">Gujarat</option>
                                <option value="Haryana">Haryana</option>
                                <option value="Himachal Pradesh">Himachal Pradesh</option>
                                <option value="Jharkhand">Jharkhand</option>
                                <option value="Karnataka">Karnataka</option>
                                <option value="Kerala">Kerala</option>
                                <option value="Madhya Pradesh">Madhya Pradesh</option>
                                <option value="Maharashtra">Maharashtra</option>
                                <option value="Manipur">Manipur</option>
                                <option value="Meghalaya">Meghalaya</option>
                                <option value="Mizoram">Mizoram</option>
                                <option value="Nagaland">Nagaland</option>
                                <option value="Odisha">Odisha</option>
                                <option value="Punjab">Punjab</option>
                                <option value="Rajasthan">Rajasthan</option>
                                <option value="Sikkim">Sikkim</option>
                                <option value="Tamil Nadu">Tamil Nadu</option>
                                <option value="Telangana">Telangana</option>
                                <option value="Tripura">Tripura</option>
                                <option value="Uttar Pradesh">Uttar Pradesh</option>
                                <option value="Uttarakhand">Uttarakhand</option>
                                <option value="West Bengal">West Bengal</option>
                                <option value="Delhi">Delhi</option>
                                <option value="Chandigarh">Chandigarh</option>
                                <option value="Puducherry">Puducherry</option>
                            </select>
                            <div class="invalid-feedback">Please select a state.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="create_contact_person" class="form-label">Contact Person</label>
                            <input type="text" class="form-control" id="create_contact_person" name="contact_person">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="create_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="create_email" name="email">
                            <div class="invalid-feedback">Please enter valid email address.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="create_phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="create_phone" name="phone">
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Create Supplier
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Supplier Modal -->
<div class="modal fade" id="editSupplierModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    Edit Supplier
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="supplier_id" id="edit_supplier_id">

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_name" class="form-label">Supplier Name *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                            <div class="invalid-feedback">Please enter supplier name.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="edit_gstin" class="form-label">GSTIN</label>
                            <input type="text" class="form-control gstin-input" id="edit_gstin" name="gstin"
                                   pattern="[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}"
                                   placeholder="22AAAAA0000A1Z5">
                            <div class="invalid-feedback">Please enter valid GSTIN format.</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_address" class="form-label">Address</label>
                        <textarea class="form-control" id="edit_address" name="address" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_state" class="form-label">State *</label>
                            <select class="form-select" id="edit_state" name="state" required>
                                <option value="">Select State</option>
                                <option value="Andhra Pradesh">Andhra Pradesh</option>
                                <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                <option value="Assam">Assam</option>
                                <option value="Bihar">Bihar</option>
                                <option value="Chhattisgarh">Chhattisgarh</option>
                                <option value="Goa">Goa</option>
                                <option value="Gujarat">Gujarat</option>
                                <option value="Haryana">Haryana</option>
                                <option value="Himachal Pradesh">Himachal Pradesh</option>
                                <option value="Jharkhand">Jharkhand</option>
                                <option value="Karnataka">Karnataka</option>
                                <option value="Kerala">Kerala</option>
                                <option value="Madhya Pradesh">Madhya Pradesh</option>
                                <option value="Maharashtra">Maharashtra</option>
                                <option value="Manipur">Manipur</option>
                                <option value="Meghalaya">Meghalaya</option>
                                <option value="Mizoram">Mizoram</option>
                                <option value="Nagaland">Nagaland</option>
                                <option value="Odisha">Odisha</option>
                                <option value="Punjab">Punjab</option>
                                <option value="Rajasthan">Rajasthan</option>
                                <option value="Sikkim">Sikkim</option>
                                <option value="Tamil Nadu">Tamil Nadu</option>
                                <option value="Telangana">Telangana</option>
                                <option value="Tripura">Tripura</option>
                                <option value="Uttar Pradesh">Uttar Pradesh</option>
                                <option value="Uttarakhand">Uttarakhand</option>
                                <option value="West Bengal">West Bengal</option>
                                <option value="Delhi">Delhi</option>
                                <option value="Chandigarh">Chandigarh</option>
                                <option value="Puducherry">Puducherry</option>
                            </select>
                            <div class="invalid-feedback">Please select a state.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="edit_contact_person" class="form-label">Contact Person</label>
                            <input type="text" class="form-control" id="edit_contact_person" name="contact_person">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="edit_email" name="email">
                            <div class="invalid-feedback">Please enter valid email address.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="edit_phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="edit_phone" name="phone">
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">
                                Active Supplier
                            </label>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Update Supplier
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import CSV Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>
                    Import Suppliers from CSV
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                <div class="modal-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>CSV Format</h6>
                        <p class="mb-2">Your CSV file should have the following columns in order:</p>
                        <ol class="mb-2">
                            <li>Name (required)</li>
                            <li>Address</li>
                            <li>State (required)</li>
                            <li>GSTIN</li>
                            <li>Contact Person</li>
                            <li>Email</li>
                            <li>Phone</li>
                        </ol>
                        <p class="mb-0"><small>First row should contain column headers.</small></p>
                    </div>

                    <div class="mb-3">
                        <label for="csv_file" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <div class="form-text">Maximum file size: 2MB</div>
                    </div>

                    <div class="mb-3">
                        <a href="../../templates/supplier_import_template.csv" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-download me-2"></i>
                            Download Template
                        </a>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>
                        Import Suppliers
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Supplier Modal -->
<div class="modal fade" id="deleteSupplierModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-trash me-2"></i>
                    Delete Supplier
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="supplier_id" id="delete_supplier_id">

                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Are you sure you want to delete this supplier?
                    </div>
                    <p>Supplier: <strong id="delete_supplier_name"></strong></p>
                    <p class="text-muted">This action cannot be undone. Suppliers with existing invoices cannot be deleted.</p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        Delete Supplier
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editSupplier(supplier) {
    document.getElementById('edit_supplier_id').value = supplier.id;
    document.getElementById('edit_name').value = supplier.name;
    document.getElementById('edit_address').value = supplier.address || '';
    document.getElementById('edit_state').value = supplier.state;
    document.getElementById('edit_gstin').value = supplier.gstin || '';
    document.getElementById('edit_contact_person').value = supplier.contact_person || '';
    document.getElementById('edit_email').value = supplier.email || '';
    document.getElementById('edit_phone').value = supplier.phone || '';
    document.getElementById('edit_is_active').checked = supplier.is_active == 1;

    new bootstrap.Modal(document.getElementById('editSupplierModal')).show();
}

function deleteSupplier(supplierId, supplierName) {
    document.getElementById('delete_supplier_id').value = supplierId;
    document.getElementById('delete_supplier_name').textContent = supplierName;

    new bootstrap.Modal(document.getElementById('deleteSupplierModal')).show();
}

// GSTIN validation
document.addEventListener('DOMContentLoaded', function() {
    const gstinInputs = document.querySelectorAll('.gstin-input');

    gstinInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            this.value = this.value.toUpperCase();

            if (this.value.length > 0) {
                const gstinPattern = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

                if (gstinPattern.test(this.value)) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else if (this.value.length === 15) {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-valid', 'is-invalid');
                }
            } else {
                this.classList.remove('is-valid', 'is-invalid');
            }
        });
    });
});
</script>

<?php include '../../includes/footer.php'; ?>
