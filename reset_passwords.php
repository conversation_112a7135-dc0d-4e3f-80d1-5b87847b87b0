<?php
/**
 * Password Reset Utility & System Debugger
 * Run this file to reset user passwords and debug authentication issues
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>GST e-Invoice - System Debugger & Password Reset</h2>";
echo "<style>
body { font-family: Arial, sans-serif; max-width: 900px; margin: 0 auto; padding: 20px; }
.success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.code { background: #f4f4f4; padding: 10px; border-radius: 5px; font-family: monospace; }
</style>";

// Step 1: Test basic PHP functionality
echo "<h3>Step 1: PHP Environment Check</h3>";
echo "<div class='info'>PHP Version: " . PHP_VERSION . "</div>";
echo "<div class='info'>Session Status: " . (session_status() == PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</div>";

// Step 2: Test database connection
echo "<h3>Step 2: Database Connection Test</h3>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        die("Cannot proceed without database connection.");
    }

    echo "<div class='success'>✅ Database connection successful!</div>";

    // Test database version
    $version = $db->query("SELECT VERSION() as version")->fetch();
    echo "<div class='info'>Database Version: " . $version['version'] . "</div>";

} catch (Exception $e) {
    echo "<div class='error'>❌ Database Error: " . $e->getMessage() . "</div>";
    die("Cannot proceed without database connection.");
}

// Step 3: Check users table structure
echo "<h3>Step 3: Users Table Structure</h3>";
try {
    $stmt = $db->query("DESCRIBE users");
    $columns = $stmt->fetchAll();

    echo "<table>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . ($col['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<div class='success'>✅ Users table structure verified</div>";

} catch (Exception $e) {
    echo "<div class='error'>❌ Users table error: " . $e->getMessage() . "</div>";
}

// Step 4: Check current user data
echo "<h3>Step 4: Current User Data</h3>";
try {
    $stmt = $db->query("SELECT id, name, email, role, is_active, failed_attempts, locked_until, created_at FROM users");
    $users = $stmt->fetchAll();

    if ($users) {
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Active</th><th>Failed Attempts</th><th>Locked Until</th><th>Created</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['name'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . $user['failed_attempts'] . "</td>";
            echo "<td>" . ($user['locked_until'] ?? 'None') . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<div class='success'>✅ Found " . count($users) . " users</div>";
    } else {
        echo "<div class='warning'>⚠️ No users found in database</div>";
    }

} catch (Exception $e) {
    echo "<div class='error'>❌ User data error: " . $e->getMessage() . "</div>";
}

// Step 5: Test password hashing
echo "<h3>Step 5: Password Hashing Test</h3>";
$test_passwords = ['Admin@123', 'Test@123'];
foreach ($test_passwords as $pwd) {
    $hash = password_hash($pwd, PASSWORD_DEFAULT);
    $verify = password_verify($pwd, $hash);
    echo "<div class='info'>Password: $pwd</div>";
    echo "<div class='code'>Hash: $hash</div>";
    echo "<div class='" . ($verify ? 'success' : 'error') . "'>" . ($verify ? '✅' : '❌') . " Verification: " . ($verify ? 'PASS' : 'FAIL') . "</div>";
    echo "<br>";
}
// Step 6: Reset passwords with proper hashing
echo "<h3>Step 6: Password Reset</h3>";

// Generate new password hashes
$admin_password = password_hash('Admin@123', PASSWORD_DEFAULT);
$user_password = password_hash('Test@123', PASSWORD_DEFAULT);

echo "<div class='info'>Generated Admin Hash: " . substr($admin_password, 0, 50) . "...</div>";
echo "<div class='info'>Generated User Hash: " . substr($user_password, 0, 50) . "...</div>";

// Check if users exist first
$admin_exists = $db->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
$admin_exists->execute();
$admin_user = $admin_exists->fetch();

$user_exists = $db->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
$user_exists->execute();
$regular_user = $user_exists->fetch();

// Create admin user if doesn't exist
if (!$admin_user) {
    echo "<div class='warning'>⚠️ Admin user doesn't exist, creating...</div>";
    $stmt = $db->prepare("INSERT INTO users (name, email, password, role, is_active, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
    if ($stmt->execute(['Admin User', '<EMAIL>', $admin_password, 'admin', 1])) {
        echo "<div class='success'>✅ Admin user created successfully</div>";
    } else {
        echo "<div class='error'>❌ Failed to create admin user</div>";
    }
} else {
    // Update existing admin password
    $stmt = $db->prepare("UPDATE users SET password = ?, failed_attempts = 0, locked_until = NULL WHERE email = '<EMAIL>'");
    if ($stmt->execute([$admin_password])) {
        echo "<div class='success'>✅ Admin password reset successfully</div>";
    } else {
        echo "<div class='error'>❌ Failed to reset admin password</div>";
    }
}

// Create regular user if doesn't exist
if (!$regular_user) {
    echo "<div class='warning'>⚠️ Regular user doesn't exist, creating...</div>";
    $stmt = $db->prepare("INSERT INTO users (name, email, password, role, is_active, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
    if ($stmt->execute(['Test User', '<EMAIL>', $user_password, 'user', 1])) {
        echo "<div class='success'>✅ Regular user created successfully</div>";
    } else {
        echo "<div class='error'>❌ Failed to create regular user</div>";
    }
} else {
    // Update existing user password
    $stmt = $db->prepare("UPDATE users SET password = ?, failed_attempts = 0, locked_until = NULL WHERE email = '<EMAIL>'");
    if ($stmt->execute([$user_password])) {
        echo "<div class='success'>✅ User password reset successfully</div>";
    } else {
        echo "<div class='error'>❌ Failed to reset user password</div>";
    }
}
// Step 7: Verify password reset worked
echo "<h3>Step 7: Password Verification Test</h3>";
$test_users = [
    ['email' => '<EMAIL>', 'password' => 'Admin@123'],
    ['email' => '<EMAIL>', 'password' => 'Test@123']
];

foreach ($test_users as $test_user) {
    $stmt = $db->prepare("SELECT id, email, password FROM users WHERE email = ?");
    $stmt->execute([$test_user['email']]);
    $user = $stmt->fetch();

    if ($user) {
        $is_valid = password_verify($test_user['password'], $user['password']);
        echo "<div class='" . ($is_valid ? 'success' : 'error') . "'>";
        echo ($is_valid ? '✅' : '❌') . " {$test_user['email']} password verification: " . ($is_valid ? 'PASS' : 'FAIL');
        echo "</div>";

        if (!$is_valid) {
            echo "<div class='error'>Expected: {$test_user['password']}</div>";
            echo "<div class='error'>Hash in DB: " . substr($user['password'], 0, 50) . "...</div>";
        }
    } else {
        echo "<div class='error'>❌ User {$test_user['email']} not found</div>";
    }
}

// Step 8: Create security_log table if missing
echo "<h3>Step 8: Security Log Table</h3>";
try {
    $create_security_log = "
    CREATE TABLE IF NOT EXISTS `security_log` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `event_type` varchar(50) NOT NULL,
      `description` text DEFAULT NULL,
      `user_id` int(11) DEFAULT NULL,
      `ip_address` varchar(45) DEFAULT NULL,
      `user_agent` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `idx_event_type` (`event_type`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_ip_address` (`ip_address`),
      KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    if ($db->exec($create_security_log) !== false) {
        echo "<div class='success'>✅ Security log table created/verified</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Security log table error: " . $e->getMessage() . "</div>";
}
    
    // Add some test recipients if none exist
    $check_recipients = $db->query("SELECT COUNT(*) as count FROM recipients")->fetch();
    if ($check_recipients['count'] == 0) {
        $recipients_sql = "
        INSERT INTO recipients (name, address, state, gstin, contact_person, email, phone, is_active, created_by, created_at) VALUES
        ('Global Tech Solutions', '100 IT Park, Phase 2\nNoida, Uttar Pradesh', 'Uttar Pradesh', '09GLBTE1234G1Z8', 'Vikram Singh', '<EMAIL>', '9876543220', 1, 1, NOW()),
        ('Modern Retail Chain', '200 Shopping Mall\nMumbai, Maharashtra', 'Maharashtra', '27MODRT5678H1Z2', 'Neha Gupta', '<EMAIL>', '9876543221', 1, 1, NOW()),
        ('Smart Electronics', '300 Electronics Market\nDelhi, Delhi', 'Delhi', '07SMTEL9012I1Z6', 'Rohit Sharma', '<EMAIL>', '9876543222', 1, 1, NOW())";
        
        if ($db->exec($recipients_sql) !== false) {
            echo "<p style='color: green;'>✅ Test recipients added</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Recipients already exist</p>";
    }
    
    // Add some test products if none exist
    $check_products = $db->query("SELECT COUNT(*) as count FROM products")->fetch();
    if ($check_products['count'] == 0) {
        $products_sql = "
        INSERT INTO products (code, name, description, hsn_sac_code, unit, tax_rate, is_active, created_by, created_at) VALUES
        ('LAP001', 'Laptop Computer', 'High-performance business laptop', '84713000', 'Nos', 18.00, 1, 1, NOW()),
        ('CHR001', 'Office Chair', 'Ergonomic office chair with lumbar support', '94013000', 'Nos', 18.00, 1, 1, NOW()),
        ('PPR001', 'Printer Paper', 'A4 size white printing paper', '48025590', 'Nos', 12.00, 1, 1, NOW()),
        ('SFT001', 'Software License', 'Annual software license subscription', '998313', 'Nos', 18.00, 1, 1, NOW()),
        ('MOB001', 'Mobile Phone', 'Smartphone with advanced features', '85171200', 'Nos', 18.00, 1, 1, NOW())";
        
        if ($db->exec($products_sql) !== false) {
            echo "<p style='color: green;'>✅ Test products added</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Products already exist</p>";
    }
    
// Step 9: Test login simulation
echo "<h3>Step 9: Login Simulation Test</h3>";
echo "<div class='info'>Testing the complete login flow...</div>";

// Start session for testing
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Test CSRF token generation
if (!function_exists('generateCSRFToken')) {
    function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

if (!function_exists('validateCSRFToken')) {
    function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}

if (!function_exists('sanitizeInput')) {
    function sanitizeInput($data) {
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
}

$csrf_token = generateCSRFToken();
echo "<div class='success'>✅ CSRF token generated: " . substr($csrf_token, 0, 20) . "...</div>";

// Test login for both users
$test_logins = [
    ['email' => '<EMAIL>', 'password' => 'Admin@123'],
    ['email' => '<EMAIL>', 'password' => 'Test@123']
];

foreach ($test_logins as $login) {
    echo "<h4>Testing login for: {$login['email']}</h4>";

    try {
        // Simulate login process
        $stmt = $db->prepare("
            SELECT id, name, email, password, role, failed_attempts, locked_until
            FROM users
            WHERE email = ? AND is_active = 1
        ");
        $stmt->execute([$login['email']]);
        $user = $stmt->fetch();

        if ($user) {
            echo "<div class='success'>✅ User found in database</div>";

            // Check if account is locked
            if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                echo "<div class='error'>❌ Account is locked</div>";
            } else {
                echo "<div class='success'>✅ Account is not locked</div>";

                // Verify password
                if (password_verify($login['password'], $user['password'])) {
                    echo "<div class='success'>✅ Password verification successful</div>";
                    echo "<div class='info'>Login would succeed for {$login['email']}</div>";
                } else {
                    echo "<div class='error'>❌ Password verification failed</div>";
                    echo "<div class='error'>This indicates a password hash mismatch</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ User not found or inactive</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Login test error: " . $e->getMessage() . "</div>";
    }
    echo "<br>";
}

echo "<hr>";
echo "<h3>🎉 System Diagnostic Complete!</h3>";
echo "<div class='success'>";
echo "<h4>✅ Setup Summary:</h4>";
echo "<ul>";
echo "<li>Database connection: Working</li>";
echo "<li>Users table: Verified</li>";
echo "<li>Password hashing: Fixed</li>";
echo "<li>Security functions: Loaded</li>";
echo "<li>CSRF tokens: Working</li>";
echo "</ul>";
echo "</div>";

echo "<div class='info'>";
echo "<h4>🔑 Login Credentials:</h4>";
echo "<ul>";
echo "<li><strong>Admin:</strong> <EMAIL> / Admin@123</li>";
echo "<li><strong>User:</strong> <EMAIL> / Test@123</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='test_login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 Test Login Form</a>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Go to Application</a>";
echo "</div>";

echo "<div class='warning'>";
echo "<p><strong>⚠️ Security Note:</strong> Delete this file (reset_passwords.php) after use for security.</p>";
echo "</div>";

} catch (Exception $e) {
    echo "<div class='error'>❌ Critical Error: " . $e->getMessage() . "</div>";
    echo "<div class='error'>Stack trace: " . $e->getTraceAsString() . "</div>";
}
?>
