<?php
/**
 * Password Reset Utility
 * Run this file once to reset user passwords to known values
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        die("Database connection failed!");
    }
    
    echo "<h2>GST e-Invoice - Password Reset Utility</h2>";
    
    // Hash the passwords
    $admin_password = password_hash('Admin@123', PASSWORD_DEFAULT);
    $user_password = password_hash('Test@123', PASSWORD_DEFAULT);
    
    // Update admin password
    $stmt = $db->prepare("UPDATE users SET password = ?, failed_attempts = 0, locked_until = NULL WHERE email = '<EMAIL>'");
    if ($stmt->execute([$admin_password])) {
        echo "<p style='color: green;'>✅ Admin password reset successfully</p>";
        echo "<p><strong>Admin Login:</strong> <EMAIL> / Admin@123</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to reset admin password</p>";
    }
    
    // Update user password
    $stmt = $db->prepare("UPDATE users SET password = ?, failed_attempts = 0, locked_until = NULL WHERE email = '<EMAIL>'");
    if ($stmt->execute([$user_password])) {
        echo "<p style='color: green;'>✅ User password reset successfully</p>";
        echo "<p><strong>User Login:</strong> <EMAIL> / Test@123</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to reset user password</p>";
    }
    
    // Add security_log table if missing
    $create_security_log = "
    CREATE TABLE IF NOT EXISTS `security_log` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `event_type` varchar(50) NOT NULL,
      `description` text DEFAULT NULL,
      `user_id` int(11) DEFAULT NULL,
      `ip_address` varchar(45) DEFAULT NULL,
      `user_agent` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `idx_event_type` (`event_type`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_ip_address` (`ip_address`),
      KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($db->exec($create_security_log) !== false) {
        echo "<p style='color: green;'>✅ Security log table created/verified</p>";
    }
    
    // Add some test recipients if none exist
    $check_recipients = $db->query("SELECT COUNT(*) as count FROM recipients")->fetch();
    if ($check_recipients['count'] == 0) {
        $recipients_sql = "
        INSERT INTO recipients (name, address, state, gstin, contact_person, email, phone, is_active, created_by, created_at) VALUES
        ('Global Tech Solutions', '100 IT Park, Phase 2\nNoida, Uttar Pradesh', 'Uttar Pradesh', '09GLBTE1234G1Z8', 'Vikram Singh', '<EMAIL>', '9876543220', 1, 1, NOW()),
        ('Modern Retail Chain', '200 Shopping Mall\nMumbai, Maharashtra', 'Maharashtra', '27MODRT5678H1Z2', 'Neha Gupta', '<EMAIL>', '9876543221', 1, 1, NOW()),
        ('Smart Electronics', '300 Electronics Market\nDelhi, Delhi', 'Delhi', '07SMTEL9012I1Z6', 'Rohit Sharma', '<EMAIL>', '9876543222', 1, 1, NOW())";
        
        if ($db->exec($recipients_sql) !== false) {
            echo "<p style='color: green;'>✅ Test recipients added</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Recipients already exist</p>";
    }
    
    // Add some test products if none exist
    $check_products = $db->query("SELECT COUNT(*) as count FROM products")->fetch();
    if ($check_products['count'] == 0) {
        $products_sql = "
        INSERT INTO products (code, name, description, hsn_sac_code, unit, tax_rate, is_active, created_by, created_at) VALUES
        ('LAP001', 'Laptop Computer', 'High-performance business laptop', '84713000', 'Nos', 18.00, 1, 1, NOW()),
        ('CHR001', 'Office Chair', 'Ergonomic office chair with lumbar support', '94013000', 'Nos', 18.00, 1, 1, NOW()),
        ('PPR001', 'Printer Paper', 'A4 size white printing paper', '48025590', 'Nos', 12.00, 1, 1, NOW()),
        ('SFT001', 'Software License', 'Annual software license subscription', '998313', 'Nos', 18.00, 1, 1, NOW()),
        ('MOB001', 'Mobile Phone', 'Smartphone with advanced features', '85171200', 'Nos', 18.00, 1, 1, NOW())";
        
        if ($db->exec($products_sql) !== false) {
            echo "<p style='color: green;'>✅ Test products added</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Products already exist</p>";
    }
    
    echo "<hr>";
    echo "<h3>✅ Setup Complete!</h3>";
    echo "<p><strong>You can now login with:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> <EMAIL> / Admin@123</li>";
    echo "<li><strong>User:</strong> <EMAIL> / Test@123</li>";
    echo "</ul>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
    echo "<p><em>Note: Delete this file (reset_passwords.php) after use for security.</em></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
