# 🧪 GST e-Invoice Application - Complete Testing Guide

## 🚀 Quick Start Testing

### Step 1: Start Your Web Server
Choose one of these options:

**Option A: XAMPP/WAMP**
1. Start Apache and MySQL in XAMPP/WAMP
2. Open browser: `http://localhost/GePP/`

**Option B: PHP Built-in Server**
1. Open command prompt in project directory
2. Run: `php -S localhost:8000`
3. Open browser: `http://localhost:8000/`

### Step 2: Reset Passwords & Test Database
1. **First, run the password reset utility:**
   - Go to: `http://localhost:8000/reset_passwords.php` (or your server URL)
   - This will:
     - Test database connection
     - Verify table structure
     - Reset user passwords
     - Test password verification
     - Create missing tables if needed

2. **Test database structure:**
   - Go to: `http://localhost:8000/test_database.php`
   - Verify all tables exist and have data

### Step 3: Test Authentication System
1. **Test login functionality:**
   - Go to: `http://localhost:8000/test_login.php`
   - Try logging in with:
     - **Admin:** <EMAIL> / Admin@123
     - **User:** <EMAIL> / Test@123

2. **Test session management:**
   - Go to: `http://localhost:8000/test_session.php`
   - Verify session data and security functions

### Step 4: Test Main Application
1. **Access main application:**
   - Go to: `http://localhost:8000/` (redirects to login)
   - Login with admin credentials
   - Should redirect to dashboard

## 🔧 Troubleshooting Common Issues

### Issue 1: "Invalid credentials" Error
**Solution:**
1. Run `reset_passwords.php` first
2. Check if password verification shows "PASS"
3. If still failing, check database connection

### Issue 2: Database Connection Failed
**Solution:**
1. Verify MySQL/MariaDB is running
2. Check database name: `gst_einvoice`
3. Verify credentials in `config/database.php`:
   - Host: localhost
   - Username: root
   - Password: (blank)

### Issue 3: Session/CSRF Token Issues
**Solution:**
1. Run `test_session.php` to check session status
2. Clear browser cookies/cache
3. Restart web server

### Issue 4: Missing Tables
**Solution:**
1. Import the database dump you provided
2. Run `reset_passwords.php` to create missing tables
3. Check `test_database.php` for table status

## 📋 Complete Testing Checklist

### ✅ Authentication System
- [ ] Password reset utility works
- [ ] Admin login (<EMAIL> / Admin@123)
- [ ] User login (<EMAIL> / Test@123)
- [ ] Failed login attempts tracking
- [ ] Account lockout after 5 failed attempts
- [ ] Session timeout (30 minutes)
- [ ] Logout functionality
- [ ] CSRF token protection

### ✅ Dashboard & Navigation
- [ ] Dashboard loads after login
- [ ] Statistics display correctly
- [ ] Navigation menu works
- [ ] Role-based access (admin vs user)

### ✅ Master Data Modules
- [ ] Suppliers module (add/edit/view)
- [ ] Recipients module (add/edit/view)
- [ ] Products module (add/edit/view)
- [ ] Data validation and GST compliance

### ✅ Invoice System
- [ ] Create new invoice
- [ ] Add invoice items
- [ ] Calculate taxes (CGST/SGST/IGST)
- [ ] Save as draft
- [ ] Submit invoice
- [ ] Generate IRN (mock)
- [ ] Print invoice with QR code

### ✅ Reports & Analytics
- [ ] Invoice reports
- [ ] Tax summaries
- [ ] Date range filtering
- [ ] Export functionality

### ✅ Security Features
- [ ] Input sanitization
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] Audit logging
- [ ] Role-based permissions

## 🔍 Detailed Module Testing

### 1. Suppliers Module
1. Go to: Dashboard → Suppliers
2. Click "Add New Supplier"
3. Fill required fields:
   - Name, GSTIN, Address, etc.
4. Test validation (invalid GSTIN format)
5. Save and verify in list

### 2. Recipients Module
1. Go to: Dashboard → Recipients
2. Add new recipient with valid data
3. Test different recipient types
4. Verify GST validation

### 3. Products Module
1. Go to: Dashboard → Products
2. Add products with different HSN codes
3. Test tax rate validation
4. Verify calculations

### 4. Invoice Creation
1. Go to: Dashboard → Invoices → Create New
2. Select supplier and recipient
3. Add multiple products
4. Verify tax calculations
5. Save as draft
6. Submit invoice
7. Check generated invoice number

### 5. Reports
1. Go to: Dashboard → Reports
2. Test different report types
3. Apply date filters
4. Export to PDF/Excel

## 🚨 Critical Test Points

### Security Testing
1. **SQL Injection:** Try entering `'; DROP TABLE users; --` in login fields
2. **XSS:** Try entering `<script>alert('test')</script>` in form fields
3. **CSRF:** Try accessing forms without proper tokens
4. **Session Hijacking:** Test session timeout and regeneration

### Performance Testing
1. Create multiple invoices
2. Test with large datasets
3. Check page load times
4. Verify database queries

### Data Integrity
1. Test foreign key constraints
2. Verify audit logging
3. Check data validation rules
4. Test backup/restore procedures

## 📞 Support & Next Steps

If you encounter any issues:

1. **Check the test utilities first:**
   - `reset_passwords.php` - Fix authentication
   - `test_database.php` - Verify database
   - `test_login.php` - Test login flow
   - `test_session.php` - Check sessions

2. **Common fixes:**
   - Clear browser cache
   - Restart web server
   - Check PHP error logs
   - Verify database connection

3. **For production deployment:**
   - Change default passwords
   - Update database credentials
   - Enable HTTPS
   - Configure proper error handling
   - Set up regular backups

## 🎯 Success Criteria

The application is working correctly when:
- ✅ All test utilities show green checkmarks
- ✅ Login works with test credentials
- ✅ Dashboard loads with statistics
- ✅ All modules are accessible
- ✅ Invoice creation works end-to-end
- ✅ Reports generate correctly
- ✅ Security features are active

**Ready for production when all checklist items are completed!**
