<?php
/**
 * Database Connection and Password Test
 * Run this to verify everything is working
 */

echo "<h2>GST e-Invoice - System Test</h2>";

// Test 1: Database Connection
echo "<h3>1. Testing Database Connection...</h3>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p style='color: green;'>✅ Database connection successful!</p>";
        
        // Test 2: Check users table
        echo "<h3>2. Testing Users Table...</h3>";
        $stmt = $db->query("SELECT id, name, email, role, is_active FROM users");
        $users = $stmt->fetchAll();
        
        if ($users) {
            echo "<p style='color: green;'>✅ Users table accessible!</p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Active</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . $user['name'] . "</td>";
                echo "<td>" . $user['email'] . "</td>";
                echo "<td>" . $user['role'] . "</td>";
                echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Test 3: Check password hashes
        echo "<h3>3. Testing Password Verification...</h3>";
        $stmt = $db->prepare("SELECT email, password FROM users WHERE email IN ('<EMAIL>', '<EMAIL>')");
        $stmt->execute();
        $users = $stmt->fetchAll();
        
        foreach ($users as $user) {
            $test_password = ($user['email'] === '<EMAIL>') ? 'Admin@123' : 'Test@123';
            $is_valid = password_verify($test_password, $user['password']);
            
            if ($is_valid) {
                echo "<p style='color: green;'>✅ Password for {$user['email']} is correct</p>";
            } else {
                echo "<p style='color: red;'>❌ Password for {$user['email']} needs to be reset</p>";
                echo "<p><strong>Expected:</strong> $test_password</p>";
            }
        }
        
        // Test 4: Check security_log table
        echo "<h3>4. Testing Security Log Table...</h3>";
        try {
            $stmt = $db->query("SHOW TABLES LIKE 'security_log'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ Security log table exists</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Security log table missing - will be created by reset script</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Security log table check failed - will be created by reset script</p>";
        }
        
        // Test 5: Check other tables
        echo "<h3>5. Testing Other Tables...</h3>";
        $tables = ['suppliers', 'recipients', 'products', 'invoices', 'invoice_items', 'audit_log'];
        foreach ($tables as $table) {
            try {
                $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
                $result = $stmt->fetch();
                echo "<p style='color: green;'>✅ Table '$table' exists with {$result['count']} records</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Table '$table' error: " . $e->getMessage() . "</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ Database connection failed!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='reset_passwords.php'>Run Password Reset Utility</a> - Fix any password issues</li>";
echo "<li><a href='index.php'>Go to Application</a> - Start using the system</li>";
echo "<li><a href='setup_instructions.html'>View Setup Instructions</a> - Complete guide</li>";
echo "</ol>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
table {
    margin: 10px 0;
}
th, td {
    padding: 8px;
    text-align: left;
}
th {
    background-color: #f2f2f2;
}
</style>
