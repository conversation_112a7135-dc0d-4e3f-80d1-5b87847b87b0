<?php
/**
 * Common Functions
 * GST e-Invoice Application
 */

// Prevent direct access
if (!defined('APP_NAME') && basename($_SERVER['PHP_SELF']) === 'functions.php') {
    die('Direct access not permitted');
}

/**
 * Database connection helper
 */
function getDBConnection() {
    try {
        $database = new Database();
        return $database->getConnection();
    } catch (Exception $e) {
        error_log('Database connection error: ' . $e->getMessage());
        return null;
    }
}

// Note: logSecurityEvent function is defined in includes/security.php

/**
 * Log audit events
 */
function logAuditEvent($table_name, $record_id, $action, $old_values = null, $new_values = null, $user_id = null) {
    try {
        $db = getDBConnection();
        if ($db) {
            $stmt = $db->prepare("
                INSERT INTO audit_log (user_id, table_name, record_id, action, old_values, new_values, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $user_id ?? $_SESSION['user_id'] ?? null,
                $table_name,
                $record_id,
                $action,
                $old_values ? json_encode($old_values) : null,
                $new_values ? json_encode($new_values) : null,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        }
    } catch (Exception $e) {
        error_log('Audit logging error: ' . $e->getMessage());
    }
}

// Note: Validation functions (validateGSTIN, validateHSNSAC, validatePAN, validatePincode)
// are defined in includes/security.php

/**
 * Get Indian states with codes
 */
function getIndianStates() {
    return [
        '01' => 'Jammu and Kashmir',
        '02' => 'Himachal Pradesh',
        '03' => 'Punjab',
        '04' => 'Chandigarh',
        '05' => 'Uttarakhand',
        '06' => 'Haryana',
        '07' => 'Delhi',
        '08' => 'Rajasthan',
        '09' => 'Uttar Pradesh',
        '10' => 'Bihar',
        '11' => 'Sikkim',
        '12' => 'Arunachal Pradesh',
        '13' => 'Nagaland',
        '14' => 'Manipur',
        '15' => 'Mizoram',
        '16' => 'Tripura',
        '17' => 'Meghalaya',
        '18' => 'Assam',
        '19' => 'West Bengal',
        '20' => 'Jharkhand',
        '21' => 'Odisha',
        '22' => 'Chhattisgarh',
        '23' => 'Madhya Pradesh',
        '24' => 'Gujarat',
        '25' => 'Daman and Diu',
        '26' => 'Dadra and Nagar Haveli',
        '27' => 'Maharashtra',
        '28' => 'Andhra Pradesh',
        '29' => 'Karnataka',
        '30' => 'Goa',
        '31' => 'Lakshadweep',
        '32' => 'Kerala',
        '33' => 'Tamil Nadu',
        '34' => 'Puducherry',
        '35' => 'Andaman and Nicobar Islands',
        '36' => 'Telangana',
        '37' => 'Andhra Pradesh'
    ];
}

/**
 * Get state code from state name
 */
function getStateCode($stateName) {
    $states = getIndianStates();
    foreach ($states as $code => $name) {
        if (strcasecmp($name, $stateName) === 0) {
            return $code;
        }
    }
    return null;
}

/**
 * Calculate tax amounts based on supplier and recipient states
 */
function calculateTax($amount, $tax_rate, $supplier_state, $recipient_state) {
    $tax_amount = ($amount * $tax_rate) / 100;
    
    // If same state, split into CGST and SGST
    if (strcasecmp($supplier_state, $recipient_state) === 0) {
        return [
            'cgst_rate' => $tax_rate / 2,
            'sgst_rate' => $tax_rate / 2,
            'igst_rate' => 0,
            'cgst_amount' => $tax_amount / 2,
            'sgst_amount' => $tax_amount / 2,
            'igst_amount' => 0,
            'total_tax' => $tax_amount
        ];
    } else {
        // Different states, use IGST
        return [
            'cgst_rate' => 0,
            'sgst_rate' => 0,
            'igst_rate' => $tax_rate,
            'cgst_amount' => 0,
            'sgst_amount' => 0,
            'igst_amount' => $tax_amount,
            'total_tax' => $tax_amount
        ];
    }
}

/**
 * Generate unique invoice number
 */
function generateInvoiceNumber() {
    try {
        $db = getDBConnection();
        if ($db) {
            $stmt = $db->prepare("CALL GenerateInvoiceNumber(@new_number)");
            $stmt->execute();
            
            $result = $db->query("SELECT @new_number as invoice_number")->fetch();
            return $result['invoice_number'];
        }
    } catch (Exception $e) {
        error_log('Invoice number generation error: ' . $e->getMessage());
    }
    
    // Fallback to simple generation
    return 'INV-' . date('Ymd') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
}

/**
 * Format number for display
 */
function formatNumber($number, $decimals = 2) {
    return number_format($number, $decimals);
}

/**
 * Convert number to words (for invoice amounts)
 */
function numberToWords($number) {
    $ones = array(
        0 => 'Zero', 1 => 'One', 2 => 'Two', 3 => 'Three', 4 => 'Four',
        5 => 'Five', 6 => 'Six', 7 => 'Seven', 8 => 'Eight', 9 => 'Nine',
        10 => 'Ten', 11 => 'Eleven', 12 => 'Twelve', 13 => 'Thirteen',
        14 => 'Fourteen', 15 => 'Fifteen', 16 => 'Sixteen', 17 => 'Seventeen',
        18 => 'Eighteen', 19 => 'Nineteen'
    );
    
    $tens = array(
        2 => 'Twenty', 3 => 'Thirty', 4 => 'Forty', 5 => 'Fifty',
        6 => 'Sixty', 7 => 'Seventy', 8 => 'Eighty', 9 => 'Ninety'
    );
    
    if ($number < 20) {
        return $ones[$number];
    } elseif ($number < 100) {
        return $tens[intval($number / 10)] . ($number % 10 != 0 ? ' ' . $ones[$number % 10] : '');
    } elseif ($number < 1000) {
        return $ones[intval($number / 100)] . ' Hundred' . ($number % 100 != 0 ? ' ' . numberToWords($number % 100) : '');
    } elseif ($number < 100000) {
        return numberToWords(intval($number / 1000)) . ' Thousand' . ($number % 1000 != 0 ? ' ' . numberToWords($number % 1000) : '');
    } elseif ($number < 10000000) {
        return numberToWords(intval($number / 100000)) . ' Lakh' . ($number % 100000 != 0 ? ' ' . numberToWords($number % 100000) : '');
    } else {
        return numberToWords(intval($number / 10000000)) . ' Crore' . ($number % 10000000 != 0 ? ' ' . numberToWords($number % 10000000) : '');
    }
}

/**
 * Send JSON response
 */
function sendJSONResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: $url");
    exit();
}

/**
 * Get and clear flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}
?>
