<?php
/**
 * Invoice Verification via QR Code
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

$invoice_id = $_GET['id'] ?? 0;
$verification_code = $_GET['code'] ?? '';

if (!$invoice_id) {
    $error_message = 'Invalid verification request.';
} else {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Get invoice details for verification
        $stmt = $db->prepare("
            SELECT 
                i.invoice_number,
                i.date,
                i.total_amount,
                i.status,
                i.irn,
                s.name as supplier_name,
                s.gstin as supplier_gstin,
                r.name as recipient_name,
                r.gstin as recipient_gstin
            FROM invoices i
            JOIN suppliers s ON i.supplier_id = s.id
            JOIN recipients r ON i.recipient_id = r.id
            WHERE i.id = ?
        ");
        $stmt->execute([$invoice_id]);
        $invoice = $stmt->fetch();
        
        if (!$invoice) {
            $error_message = 'Invoice not found or invalid.';
        } else {
            // Log verification attempt
            logSecurityEvent('invoice_verification', "Invoice {$invoice['invoice_number']} verified via QR code", null, $_SERVER['REMOTE_ADDR'] ?? '');
        }
        
    } catch (Exception $e) {
        error_log('Invoice verification error: ' . $e->getMessage());
        $error_message = 'Verification system temporarily unavailable.';
    }
}

$page_title = 'Invoice Verification';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - GST e-Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .verification-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .verification-header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .verification-body {
            padding: 30px;
        }
        
        .status-badge {
            font-size: 1.1em;
            padding: 8px 16px;
            border-radius: 20px;
        }
        
        .verification-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        
        .detail-value {
            color: #212529;
            text-align: right;
        }
        
        .verification-footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .error-container {
            text-align: center;
            padding: 40px;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="verification-container">
            <?php if (isset($error_message)): ?>
                <!-- Error State -->
                <div class="verification-header" style="background: linear-gradient(45deg, #dc3545, #c82333);">
                    <i class="fas fa-exclamation-triangle error-icon"></i>
                    <h2>Verification Failed</h2>
                </div>
                <div class="error-container">
                    <p class="lead text-danger"><?php echo htmlspecialchars($error_message); ?></p>
                    <p class="text-muted">Please check the QR code and try again, or contact the invoice issuer for assistance.</p>
                </div>
            <?php else: ?>
                <!-- Success State -->
                <div class="verification-header">
                    <i class="fas fa-check-circle success-icon"></i>
                    <h2>Invoice Verified</h2>
                    <p class="mb-0">This invoice is authentic and valid</p>
                </div>
                
                <div class="verification-body">
                    <div class="text-center mb-4">
                        <span class="status-badge bg-<?php echo $invoice['status'] === 'submitted' ? 'success' : 'warning'; ?> text-white">
                            <i class="fas fa-<?php echo $invoice['status'] === 'submitted' ? 'check' : 'clock'; ?> me-2"></i>
                            <?php echo ucfirst($invoice['status']); ?>
                        </span>
                    </div>
                    
                    <div class="verification-details">
                        <div class="detail-row">
                            <span class="detail-label">
                                <i class="fas fa-file-invoice me-2"></i>
                                Invoice Number
                            </span>
                            <span class="detail-value">
                                <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">
                                <i class="fas fa-calendar me-2"></i>
                                Invoice Date
                            </span>
                            <span class="detail-value">
                                <?php echo date('d M Y', strtotime($invoice['date'])); ?>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">
                                <i class="fas fa-building me-2"></i>
                                Supplier
                            </span>
                            <span class="detail-value">
                                <?php echo htmlspecialchars($invoice['supplier_name']); ?>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">
                                <i class="fas fa-id-card me-2"></i>
                                Supplier GSTIN
                            </span>
                            <span class="detail-value">
                                <code><?php echo htmlspecialchars($invoice['supplier_gstin']); ?></code>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">
                                <i class="fas fa-user me-2"></i>
                                Recipient
                            </span>
                            <span class="detail-value">
                                <?php echo htmlspecialchars($invoice['recipient_name']); ?>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">
                                <i class="fas fa-id-card me-2"></i>
                                Recipient GSTIN
                            </span>
                            <span class="detail-value">
                                <code><?php echo htmlspecialchars($invoice['recipient_gstin']); ?></code>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">
                                <i class="fas fa-rupee-sign me-2"></i>
                                Total Amount
                            </span>
                            <span class="detail-value">
                                <strong class="text-success">₹<?php echo number_format($invoice['total_amount'], 2); ?></strong>
                            </span>
                        </div>
                        
                        <?php if ($invoice['irn']): ?>
                        <div class="detail-row">
                            <span class="detail-label">
                                <i class="fas fa-barcode me-2"></i>
                                IRN
                            </span>
                            <span class="detail-value">
                                <small><code><?php echo htmlspecialchars($invoice['irn']); ?></code></small>
                            </span>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Verification Details:</strong><br>
                        This invoice has been verified against our secure database. 
                        The information displayed above matches our records and confirms the authenticity of this document.
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="verification-footer">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            Verified on <?php echo date('d M Y H:i:s'); ?>
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-globe me-1"></i>
                            GST e-Invoice System
                        </small>
                    </div>
                </div>
                
                <?php if (!isset($error_message)): ?>
                <div class="mt-3">
                    <a href="../../index.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home me-2"></i>
                        Visit Our System
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Additional Security Information -->
        <div class="text-center mt-4">
            <div class="card bg-dark text-white">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lock me-2"></i>
                        Security Notice
                    </h6>
                    <p class="card-text small mb-0">
                        This verification page uses secure protocols to validate invoice authenticity. 
                        If you have concerns about this invoice, please contact the issuing organization directly.
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
