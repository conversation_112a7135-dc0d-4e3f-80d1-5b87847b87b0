<?php
/**
 * Data Export Functionality
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login
requireLogin();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $export_type = $_GET['type'] ?? 'invoices';
    $format = $_GET['format'] ?? 'csv';
    $date_from = $_GET['date_from'] ?? date('Y-m-01');
    $date_to = $_GET['date_to'] ?? date('Y-m-t');
    $state_filter = $_GET['state'] ?? '';
    
    // User condition for non-admin users
    $user_condition = '';
    $user_params = [];
    if (!isAdmin()) {
        $user_condition = ' AND i.user_id = ?';
        $user_params[] = $_SESSION['user_id'];
    }
    
    // Set headers for download
    $filename = $export_type . '_' . date('Y-m-d_H-i-s') . '.' . $format;
    
    if ($format === 'csv') {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        $output = fopen('php://output', 'w');
    }
    
    switch ($export_type) {
        case 'invoices':
            exportInvoices($db, $output, $date_from, $date_to, $user_condition, $user_params, $state_filter);
            break;
            
        case 'gst_summary':
            exportGSTSummary($db, $output, $date_from, $date_to, $user_condition, $user_params, $state_filter);
            break;
            
        case 'suppliers':
            exportSuppliers($db, $output, $user_condition, $user_params);
            break;
            
        case 'recipients':
            exportRecipients($db, $output, $user_condition, $user_params);
            break;
            
        case 'products':
            exportProducts($db, $output, $user_condition, $user_params);
            break;
            
        default:
            throw new Exception('Invalid export type');
    }
    
    if ($format === 'csv') {
        fclose($output);
    }
    
} catch (Exception $e) {
    error_log('Export error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'Export failed: ' . $e->getMessage();
    header('Location: index.php');
    exit();
}

function exportInvoices($db, $output, $date_from, $date_to, $user_condition, $user_params, $state_filter) {
    // CSV headers
    fputcsv($output, [
        'Invoice Number', 'Date', 'Supplier Name', 'Supplier GSTIN', 
        'Recipient Name', 'Recipient GSTIN', 'Place of Supply', 
        'Subtotal', 'CGST', 'SGST', 'IGST', 'Total Amount', 'Status'
    ]);
    
    $query = "
        SELECT 
            i.invoice_number,
            i.date,
            s.name as supplier_name,
            s.gstin as supplier_gstin,
            r.name as recipient_name,
            r.gstin as recipient_gstin,
            i.place_of_supply,
            i.subtotal,
            i.cgst_amount,
            i.sgst_amount,
            i.igst_amount,
            i.total_amount,
            i.status
        FROM invoices i
        JOIN suppliers s ON i.supplier_id = s.id
        JOIN recipients r ON i.recipient_id = r.id
        WHERE i.date BETWEEN ? AND ?
        $user_condition
    ";
    
    $params = [$date_from, $date_to];
    $params = array_merge($params, $user_params);
    
    if (!empty($state_filter)) {
        $query .= " AND i.place_of_supply = ?";
        $params[] = $state_filter;
    }
    
    $query .= " ORDER BY i.date DESC, i.invoice_number";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    
    while ($row = $stmt->fetch()) {
        fputcsv($output, [
            $row['invoice_number'],
            $row['date'],
            $row['supplier_name'],
            $row['supplier_gstin'],
            $row['recipient_name'],
            $row['recipient_gstin'],
            $row['place_of_supply'],
            number_format($row['subtotal'], 2),
            number_format($row['cgst_amount'], 2),
            number_format($row['sgst_amount'], 2),
            number_format($row['igst_amount'], 2),
            number_format($row['total_amount'], 2),
            $row['status']
        ]);
    }
}

function exportGSTSummary($db, $output, $date_from, $date_to, $user_condition, $user_params, $state_filter) {
    // CSV headers
    fputcsv($output, [
        'State/UT', 'Invoice Count', 'Taxable Amount', 'CGST', 'SGST', 'IGST', 'Total Tax', 'Total Amount'
    ]);
    
    $query = "
        SELECT 
            i.place_of_supply,
            COUNT(i.id) as invoice_count,
            SUM(i.subtotal) as total_taxable_amount,
            SUM(i.cgst_amount) as total_cgst,
            SUM(i.sgst_amount) as total_sgst,
            SUM(i.igst_amount) as total_igst,
            SUM(i.total_amount) as total_amount
        FROM invoices i
        WHERE i.date BETWEEN ? AND ?
        AND i.status = 'submitted'
        $user_condition
    ";
    
    $params = [$date_from, $date_to];
    $params = array_merge($params, $user_params);
    
    if (!empty($state_filter)) {
        $query .= " AND i.place_of_supply = ?";
        $params[] = $state_filter;
    }
    
    $query .= " GROUP BY i.place_of_supply ORDER BY total_amount DESC";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    
    while ($row = $stmt->fetch()) {
        $total_tax = $row['total_cgst'] + $row['total_sgst'] + $row['total_igst'];
        fputcsv($output, [
            $row['place_of_supply'],
            $row['invoice_count'],
            number_format($row['total_taxable_amount'], 2),
            number_format($row['total_cgst'], 2),
            number_format($row['total_sgst'], 2),
            number_format($row['total_igst'], 2),
            number_format($total_tax, 2),
            number_format($row['total_amount'], 2)
        ]);
    }
}

function exportSuppliers($db, $output, $user_condition, $user_params) {
    // CSV headers
    fputcsv($output, [
        'Name', 'Address', 'State', 'Pincode', 'GSTIN', 'Email', 'Phone', 'Created Date'
    ]);
    
    $query = "SELECT * FROM suppliers WHERE 1=1";
    
    if (!isAdmin()) {
        $query .= " AND user_id = ?";
    }
    
    $query .= " ORDER BY name";
    
    $stmt = $db->prepare($query);
    if (!isAdmin()) {
        $stmt->execute([$_SESSION['user_id']]);
    } else {
        $stmt->execute();
    }
    
    while ($row = $stmt->fetch()) {
        fputcsv($output, [
            $row['name'],
            $row['address'],
            $row['state'],
            $row['pincode'],
            $row['gstin'],
            $row['email'],
            $row['phone'],
            $row['created_at']
        ]);
    }
}

function exportRecipients($db, $output, $user_condition, $user_params) {
    // CSV headers
    fputcsv($output, [
        'Name', 'Address', 'State', 'Pincode', 'GSTIN', 'Email', 'Phone', 'Created Date'
    ]);
    
    $query = "SELECT * FROM recipients WHERE 1=1";
    
    if (!isAdmin()) {
        $query .= " AND user_id = ?";
    }
    
    $query .= " ORDER BY name";
    
    $stmt = $db->prepare($query);
    if (!isAdmin()) {
        $stmt->execute([$_SESSION['user_id']]);
    } else {
        $stmt->execute();
    }
    
    while ($row = $stmt->fetch()) {
        fputcsv($output, [
            $row['name'],
            $row['address'],
            $row['state'],
            $row['pincode'],
            $row['gstin'],
            $row['email'],
            $row['phone'],
            $row['created_at']
        ]);
    }
}

function exportProducts($db, $output, $user_condition, $user_params) {
    // CSV headers
    fputcsv($output, [
        'Name', 'Description', 'HSN/SAC Code', 'Unit', 'Rate', 'Tax Rate (%)', 'Created Date'
    ]);
    
    $query = "SELECT * FROM products WHERE 1=1";
    
    if (!isAdmin()) {
        $query .= " AND user_id = ?";
    }
    
    $query .= " ORDER BY name";
    
    $stmt = $db->prepare($query);
    if (!isAdmin()) {
        $stmt->execute([$_SESSION['user_id']]);
    } else {
        $stmt->execute();
    }
    
    while ($row = $stmt->fetch()) {
        fputcsv($output, [
            $row['name'],
            $row['description'],
            $row['hsn_sac_code'],
            $row['unit'],
            number_format($row['rate'], 2),
            $row['tax_rate'],
            $row['created_at']
        ]);
    }
}
?>
