<?php
/**
 * Reports Dashboard
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login
requireLogin();

$page_title = 'Reports Dashboard';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get date range from parameters
    $date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
    $date_to = $_GET['date_to'] ?? date('Y-m-d'); // Today
    
    // Build user restriction
    $user_condition = '';
    $user_params = [];
    if (!isAdmin()) {
        $user_condition = ' AND i.user_id = ?';
        $user_params = [$_SESSION['user_id']];
    }
    
    // Get overall statistics
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_invoices,
            SUM(i.total_amount) as total_amount,
            SUM(i.cgst_amount + i.sgst_amount + i.igst_amount) as total_tax,
            AVG(i.total_amount) as avg_invoice_amount,
            SUM(CASE WHEN i.status = 'draft' THEN 1 ELSE 0 END) as draft_count,
            SUM(CASE WHEN i.status = 'sent' THEN 1 ELSE 0 END) as sent_count,
            SUM(CASE WHEN i.status = 'paid' THEN 1 ELSE 0 END) as paid_count
        FROM invoices i
        WHERE i.date BETWEEN ? AND ?
        $user_condition
    ");
    $stmt->execute(array_merge([$date_from, $date_to], $user_params));
    $overall_stats = $stmt->fetch();
    
    // Get monthly trend data (last 12 months)
    $stmt = $db->prepare("
        SELECT 
            DATE_FORMAT(i.date, '%Y-%m') as month,
            COUNT(*) as invoice_count,
            SUM(i.total_amount) as total_amount,
            SUM(i.cgst_amount + i.sgst_amount + i.igst_amount) as tax_amount
        FROM invoices i
        WHERE i.date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        $user_condition
        GROUP BY DATE_FORMAT(i.date, '%Y-%m')
        ORDER BY month
    ");
    $stmt->execute($user_params);
    $monthly_trends = $stmt->fetchAll();
    
    // Get top suppliers by invoice count
    $stmt = $db->prepare("
        SELECT 
            s.name,
            s.gstin,
            COUNT(i.id) as invoice_count,
            SUM(i.total_amount) as total_amount
        FROM suppliers s
        JOIN invoices i ON s.id = i.supplier_id
        WHERE i.date BETWEEN ? AND ?
        $user_condition
        GROUP BY s.id, s.name, s.gstin
        ORDER BY invoice_count DESC
        LIMIT 10
    ");
    $stmt->execute(array_merge([$date_from, $date_to], $user_params));
    $top_suppliers = $stmt->fetchAll();
    
    // Get top recipients by amount
    $stmt = $db->prepare("
        SELECT 
            r.name,
            r.gstin,
            COUNT(i.id) as invoice_count,
            SUM(i.total_amount) as total_amount
        FROM recipients r
        JOIN invoices i ON r.id = i.recipient_id
        WHERE i.date BETWEEN ? AND ?
        $user_condition
        GROUP BY r.id, r.name, r.gstin
        ORDER BY total_amount DESC
        LIMIT 10
    ");
    $stmt->execute(array_merge([$date_from, $date_to], $user_params));
    $top_recipients = $stmt->fetchAll();
    
    // Get tax summary by state
    $stmt = $db->prepare("
        SELECT 
            i.place_of_supply as state,
            COUNT(i.id) as invoice_count,
            SUM(i.subtotal) as subtotal,
            SUM(i.cgst_amount) as cgst_amount,
            SUM(i.sgst_amount) as sgst_amount,
            SUM(i.igst_amount) as igst_amount,
            SUM(i.total_amount) as total_amount
        FROM invoices i
        WHERE i.date BETWEEN ? AND ?
        $user_condition
        GROUP BY i.place_of_supply
        ORDER BY total_amount DESC
    ");
    $stmt->execute(array_merge([$date_from, $date_to], $user_params));
    $tax_by_state = $stmt->fetchAll();
    
    // Get recent invoices
    $stmt = $db->prepare("
        SELECT 
            i.invoice_number,
            i.date,
            i.total_amount,
            i.status,
            s.name as supplier_name,
            r.name as recipient_name
        FROM invoices i
        JOIN suppliers s ON i.supplier_id = s.id
        JOIN recipients r ON i.recipient_id = r.id
        WHERE i.date BETWEEN ? AND ?
        $user_condition
        ORDER BY i.created_at DESC
        LIMIT 10
    ");
    $stmt->execute(array_merge([$date_from, $date_to], $user_params));
    $recent_invoices = $stmt->fetchAll();
    
    // Prepare chart data
    $chart_months = [];
    $chart_amounts = [];
    $chart_counts = [];
    
    foreach ($monthly_trends as $trend) {
        $chart_months[] = date('M Y', strtotime($trend['month'] . '-01'));
        $chart_amounts[] = (float)$trend['total_amount'];
        $chart_counts[] = (int)$trend['invoice_count'];
    }
    
} catch (Exception $e) {
    error_log('Reports dashboard error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while loading reports.';
    $overall_stats = ['total_invoices' => 0, 'total_amount' => 0, 'total_tax' => 0, 'avg_invoice_amount' => 0, 'draft_count' => 0, 'sent_count' => 0, 'paid_count' => 0];
    $monthly_trends = [];
    $top_suppliers = [];
    $top_recipients = [];
    $tax_by_state = [];
    $recent_invoices = [];
    $chart_months = [];
    $chart_amounts = [];
    $chart_counts = [];
}

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-bar me-2"></i>
                Reports Dashboard
            </h2>
            <div>
                <a href="gst_summary.php" class="btn btn-outline-primary">
                    <i class="fas fa-file-alt me-2"></i>
                    GST Summary
                </a>
                <a href="export.php" class="btn btn-outline-success">
                    <i class="fas fa-download me-2"></i>
                    Export Data
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>
                                Apply Filter
                            </button>
                            <a href="dashboard.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Invoices</h6>
                        <h3 class="mb-0"><?php echo number_format($overall_stats['total_invoices']); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Amount</h6>
                        <h3 class="mb-0">₹<?php echo number_format($overall_stats['total_amount'], 2); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-rupee-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Tax</h6>
                        <h3 class="mb-0">₹<?php echo number_format($overall_stats['total_tax'], 2); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Avg Invoice</h6>
                        <h3 class="mb-0">₹<?php echo number_format($overall_stats['avg_invoice_amount'], 2); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calculator fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Monthly Trends (Last 12 Months)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyTrendChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Invoice Status
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
