<?php
/**
 * Database Test Utility
 * Comprehensive test of database structure and data
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🗄️ Database Test Utility</h2>";
echo "<style>
body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
.success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 10px; }
</style>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // Test 1: Check all required tables
    echo "<div class='section'>";
    echo "<h3>📋 Table Structure Test</h3>";
    
    $required_tables = [
        'users', 'suppliers', 'recipients', 'products', 
        'invoices', 'invoice_items', 'audit_log', 'security_log'
    ];
    
    $existing_tables = [];
    $stmt = $db->query("SHOW TABLES");
    while ($row = $stmt->fetch()) {
        $existing_tables[] = array_values($row)[0];
    }
    
    echo "<div class='info'>Found tables: " . implode(', ', $existing_tables) . "</div>";
    
    foreach ($required_tables as $table) {
        if (in_array($table, $existing_tables)) {
            echo "<div class='success'>✅ Table '$table' exists</div>";
            
            // Get row count
            try {
                $count_stmt = $db->query("SELECT COUNT(*) as count FROM $table");
                $count = $count_stmt->fetch()['count'];
                echo "<div class='info'>   → $count records</div>";
            } catch (Exception $e) {
                echo "<div class='warning'>   → Could not count records: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='error'>❌ Table '$table' missing</div>";
        }
    }
    echo "</div>";
    
    // Test 2: Users table detailed check
    echo "<div class='section'>";
    echo "<h3>👥 Users Table Analysis</h3>";
    
    try {
        $stmt = $db->query("SELECT * FROM users");
        $users = $stmt->fetchAll();
        
        if ($users) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Active</th><th>Failed Attempts</th><th>Locked</th><th>Last Login</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . $user['name'] . "</td>";
                echo "<td>" . $user['email'] . "</td>";
                echo "<td>" . $user['role'] . "</td>";
                echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
                echo "<td>" . $user['failed_attempts'] . "</td>";
                echo "<td>" . ($user['locked_until'] ?? 'No') . "</td>";
                echo "<td>" . ($user['last_login'] ?? 'Never') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Test password verification for each user
            echo "<h4>🔐 Password Verification Test</h4>";
            $test_passwords = [
                '<EMAIL>' => 'Admin@123',
                '<EMAIL>' => 'Test@123'
            ];
            
            foreach ($users as $user) {
                if (isset($test_passwords[$user['email']])) {
                    $test_password = $test_passwords[$user['email']];
                    $is_valid = password_verify($test_password, $user['password']);
                    echo "<div class='" . ($is_valid ? 'success' : 'error') . "'>";
                    echo ($is_valid ? '✅' : '❌') . " {$user['email']} password: " . ($is_valid ? 'CORRECT' : 'INCORRECT');
                    echo "</div>";
                }
            }
        } else {
            echo "<div class='warning'>⚠️ No users found</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Users table error: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    // Test 3: Master data tables
    echo "<div class='section'>";
    echo "<h3>📊 Master Data Tables</h3>";
    
    $master_tables = ['suppliers', 'recipients', 'products'];
    foreach ($master_tables as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "<div class='info'>$table: $count records</div>";
            
            if ($count > 0) {
                $stmt = $db->query("SELECT * FROM $table LIMIT 3");
                $records = $stmt->fetchAll();
                echo "<table>";
                if ($records) {
                    // Header
                    echo "<tr>";
                    foreach (array_keys($records[0]) as $column) {
                        echo "<th>$column</th>";
                    }
                    echo "</tr>";
                    
                    // Data (first 3 records)
                    foreach ($records as $record) {
                        echo "<tr>";
                        foreach ($record as $value) {
                            echo "<td>" . htmlspecialchars(substr($value ?? '', 0, 50)) . "</td>";
                        }
                        echo "</tr>";
                    }
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ $table error: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    // Test 4: Invoice system
    echo "<div class='section'>";
    echo "<h3>🧾 Invoice System Test</h3>";
    
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM invoices");
        $invoice_count = $stmt->fetch()['count'];
        echo "<div class='info'>Total invoices: $invoice_count</div>";
        
        $stmt = $db->query("SELECT COUNT(*) as count FROM invoice_items");
        $item_count = $stmt->fetch()['count'];
        echo "<div class='info'>Total invoice items: $item_count</div>";
        
        if ($invoice_count > 0) {
            $stmt = $db->query("SELECT * FROM invoices LIMIT 3");
            $invoices = $stmt->fetchAll();
            echo "<h4>Sample Invoices:</h4>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Number</th><th>Date</th><th>Supplier</th><th>Recipient</th><th>Total</th><th>Status</th></tr>";
            foreach ($invoices as $invoice) {
                echo "<tr>";
                echo "<td>" . $invoice['id'] . "</td>";
                echo "<td>" . $invoice['invoice_number'] . "</td>";
                echo "<td>" . $invoice['invoice_date'] . "</td>";
                echo "<td>" . $invoice['supplier_id'] . "</td>";
                echo "<td>" . $invoice['recipient_id'] . "</td>";
                echo "<td>₹" . number_format($invoice['total_amount'], 2) . "</td>";
                echo "<td>" . $invoice['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Invoice system error: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    // Test 5: Audit and security logs
    echo "<div class='section'>";
    echo "<h3>🔒 Security & Audit Logs</h3>";
    
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM audit_log");
        $audit_count = $stmt->fetch()['count'];
        echo "<div class='info'>Audit log entries: $audit_count</div>";
        
        if ($audit_count > 0) {
            $stmt = $db->query("SELECT * FROM audit_log ORDER BY created_at DESC LIMIT 5");
            $logs = $stmt->fetchAll();
            echo "<h4>Recent Audit Entries:</h4>";
            echo "<table>";
            echo "<tr><th>User</th><th>Table</th><th>Action</th><th>Time</th></tr>";
            foreach ($logs as $log) {
                echo "<tr>";
                echo "<td>" . $log['user_id'] . "</td>";
                echo "<td>" . $log['table_name'] . "</td>";
                echo "<td>" . $log['action'] . "</td>";
                echo "<td>" . $log['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div class='warning'>⚠️ Audit log error: " . $e->getMessage() . "</div>";
    }
    
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM security_log");
        $security_count = $stmt->fetch()['count'];
        echo "<div class='info'>Security log entries: $security_count</div>";
    } catch (Exception $e) {
        echo "<div class='warning'>⚠️ Security log table may not exist: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h3>✅ Database Test Complete</h3>";
    echo "<p>Database structure and data verified successfully!</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Critical database error: " . $e->getMessage() . "</div>";
}
?>

<div style="text-align: center; margin: 20px 0;">
    <a href="reset_passwords.php" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🔧 Reset Passwords</a>
    <a href="test_login.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🧪 Test Login</a>
    <a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🚀 Application</a>
</div>
