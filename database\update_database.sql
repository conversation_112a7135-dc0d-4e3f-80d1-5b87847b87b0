-- Database Updates for GST e-Invoice Application
-- Run this to add missing tables and fix user passwords

-- Add security_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS `security_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_type` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `idx_event_type` (`event_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Update user passwords with correct hashes
-- Admin password: Admin@123
-- User password: Test@123
UPDATE `users` SET 
  `password` = '$2y$10$YourHashedPasswordHere',
  `failed_attempts` = 0,
  `locked_until` = NULL
WHERE `email` = '<EMAIL>';

UPDATE `users` SET 
  `password` = '$2y$10$YourHashedPasswordHere',
  `failed_attempts` = 0,
  `locked_until` = NULL
WHERE `email` = '<EMAIL>';

-- Add some test recipients if none exist
INSERT IGNORE INTO `recipients` (`id`, `name`, `address`, `state`, `gstin`, `contact_person`, `email`, `phone`, `is_active`, `created_by`, `created_at`) VALUES
(1, 'Global Tech Solutions', '100 IT Park, Phase 2\nNoida, Uttar Pradesh', 'Uttar Pradesh', '09GLBTE1234G1Z8', 'Vikram Singh', '<EMAIL>', '9876543220', 1, 1, NOW()),
(2, 'Modern Retail Chain', '200 Shopping Mall\nMumbai, Maharashtra', 'Maharashtra', '27MODRT5678H1Z2', 'Neha Gupta', '<EMAIL>', '9876543221', 1, 1, NOW()),
(3, 'Smart Electronics', '300 Electronics Market\nDelhi, Delhi', 'Delhi', '07SMTEL9012I1Z6', 'Rohit Sharma', '<EMAIL>', '9876543222', 1, 1, NOW());

-- Add some test products if none exist
INSERT IGNORE INTO `products` (`id`, `code`, `name`, `description`, `hsn_sac_code`, `unit`, `tax_rate`, `is_active`, `created_by`, `created_at`) VALUES
(1, 'LAP001', 'Laptop Computer', 'High-performance business laptop', '84713000', 'Nos', 18.00, 1, 1, NOW()),
(2, 'CHR001', 'Office Chair', 'Ergonomic office chair with lumbar support', '94013000', 'Nos', 18.00, 1, 1, NOW()),
(3, 'PPR001', 'Printer Paper', 'A4 size white printing paper', '48025590', 'Nos', 12.00, 1, 1, NOW()),
(4, 'SFT001', 'Software License', 'Annual software license subscription', '998313', 'Nos', 18.00, 1, 1, NOW()),
(5, 'MOB001', 'Mobile Phone', 'Smartphone with advanced features', '85171200', 'Nos', 18.00, 1, 1, NOW());
