<?php
/**
 * Login Test Utility
 * Test the login functionality without going through the full UI
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<h2>🧪 Login Test Utility</h2>";
echo "<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
.success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
.form-container { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }
input[type='email'], input[type='password'] { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 5px; }
button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
button:hover { background: #0056b3; }
</style>";

// Include required files
require_once 'config/config.php';

$message = '';
$message_type = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    echo "<h3>🔍 Login Attempt Analysis</h3>";
    echo "<div class='info'>Email: $email</div>";
    echo "<div class='info'>Password: " . str_repeat('*', strlen($password)) . "</div>";
    
    // Validate CSRF token
    if (!validateCSRFToken($csrf_token)) {
        $message = 'Invalid CSRF token. Please try again.';
        $message_type = 'error';
        echo "<div class='error'>❌ CSRF token validation failed</div>";
    } else {
        echo "<div class='success'>✅ CSRF token validation passed</div>";
        
        // Validate inputs
        if (empty($email) || empty($password)) {
            $message = 'Please enter both email and password.';
            $message_type = 'error';
            echo "<div class='error'>❌ Empty email or password</div>";
        } else {
            echo "<div class='success'>✅ Input validation passed</div>";
            
            try {
                $database = new Database();
                $db = $database->getConnection();
                echo "<div class='success'>✅ Database connection established</div>";
                
                // Check if user exists and is not locked
                $stmt = $db->prepare("
                    SELECT id, name, email, password, role, failed_attempts, locked_until 
                    FROM users 
                    WHERE email = ? AND is_active = 1
                ");
                $stmt->execute([$email]);
                $user = $stmt->fetch();
                
                if ($user) {
                    echo "<div class='success'>✅ User found in database</div>";
                    echo "<div class='info'>User ID: {$user['id']}</div>";
                    echo "<div class='info'>User Name: {$user['name']}</div>";
                    echo "<div class='info'>User Role: {$user['role']}</div>";
                    echo "<div class='info'>Failed Attempts: {$user['failed_attempts']}</div>";
                    echo "<div class='info'>Locked Until: " . ($user['locked_until'] ?? 'Not locked') . "</div>";
                    
                    // Check if account is locked
                    if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                        $remaining = ceil((strtotime($user['locked_until']) - time()) / 60);
                        $message = "Account is locked. Try again in $remaining minutes.";
                        $message_type = 'error';
                        echo "<div class='error'>❌ Account is locked for $remaining minutes</div>";
                    } else {
                        echo "<div class='success'>✅ Account is not locked</div>";
                        
                        // Verify password
                        echo "<div class='info'>Testing password verification...</div>";
                        echo "<div class='info'>Password hash in DB: " . substr($user['password'], 0, 50) . "...</div>";
                        
                        if (password_verify($password, $user['password'])) {
                            echo "<div class='success'>✅ Password verification successful!</div>";
                            
                            // Reset failed attempts and unlock account
                            $stmt = $db->prepare("
                                UPDATE users 
                                SET failed_attempts = 0, locked_until = NULL, last_login = NOW() 
                                WHERE id = ?
                            ");
                            $stmt->execute([$user['id']]);
                            echo "<div class='success'>✅ Failed attempts reset</div>";
                            
                            // Set session variables
                            $_SESSION['user_id'] = $user['id'];
                            $_SESSION['user_name'] = $user['name'];
                            $_SESSION['user_email'] = $user['email'];
                            $_SESSION['user_role'] = $user['role'];
                            $_SESSION['login_time'] = time();
                            
                            echo "<div class='success'>✅ Session variables set</div>";
                            echo "<div class='info'>Session User ID: " . $_SESSION['user_id'] . "</div>";
                            echo "<div class='info'>Session User Name: " . $_SESSION['user_name'] . "</div>";
                            echo "<div class='info'>Session User Role: " . $_SESSION['user_role'] . "</div>";
                            
                            $message = "Login successful! You can now access the application.";
                            $message_type = 'success';
                            
                            echo "<div class='success'>";
                            echo "<h4>🎉 LOGIN SUCCESSFUL!</h4>";
                            echo "<p>You are now logged in as: {$user['name']} ({$user['role']})</p>";
                            echo "<p><a href='dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Dashboard</a></p>";
                            echo "</div>";
                            
                        } else {
                            echo "<div class='error'>❌ Password verification failed</div>";
                            
                            // Test with manual hash generation
                            $test_hash = password_hash($password, PASSWORD_DEFAULT);
                            $test_verify = password_verify($password, $test_hash);
                            echo "<div class='info'>Test hash generation: " . ($test_verify ? 'Working' : 'Failed') . "</div>";
                            
                            // Increment failed attempts
                            $failed_attempts = $user['failed_attempts'] + 1;
                            $locked_until = null;
                            
                            if ($failed_attempts >= MAX_LOGIN_ATTEMPTS) {
                                $locked_until = date('Y-m-d H:i:s', time() + LOCKOUT_DURATION);
                                $message = 'Too many failed attempts. Account locked for ' . (LOCKOUT_DURATION / 60) . ' minutes.';
                                $message_type = 'error';
                                echo "<div class='error'>❌ Account will be locked due to too many failed attempts</div>";
                            } else {
                                $remaining_attempts = MAX_LOGIN_ATTEMPTS - $failed_attempts;
                                $message = "Invalid credentials. $remaining_attempts attempts remaining.";
                                $message_type = 'error';
                                echo "<div class='error'>❌ $remaining_attempts attempts remaining</div>";
                            }
                            
                            $stmt = $db->prepare("
                                UPDATE users 
                                SET failed_attempts = ?, locked_until = ? 
                                WHERE id = ?
                            ");
                            $stmt->execute([$failed_attempts, $locked_until, $user['id']]);
                            echo "<div class='info'>Failed attempts updated to: $failed_attempts</div>";
                        }
                    }
                } else {
                    echo "<div class='error'>❌ User not found or inactive</div>";
                    $message = 'Invalid email or password.';
                    $message_type = 'error';
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
                $message = 'Login failed. Please try again.';
                $message_type = 'error';
                error_log('Login error: ' . $e->getMessage());
            }
        }
    }
}

// Check if already logged in
if (isset($_SESSION['user_id'])) {
    echo "<div class='success'>";
    echo "<h3>✅ Already Logged In</h3>";
    echo "<p>User: " . $_SESSION['user_name'] . " (" . $_SESSION['user_role'] . ")</p>";
    echo "<p><a href='dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Dashboard</a></p>";
    echo "<p><a href='modules/auth/logout.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Logout</a></p>";
    echo "</div>";
}
?>

<div class="form-container">
    <h3>🔐 Test Login Form</h3>
    
    <?php if (!empty($message)): ?>
        <div class="<?php echo $message_type; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>
    
    <form method="POST">
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        
        <label for="email">Email Address:</label>
        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
        
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required>
        
        <button type="submit">🔍 Test Login</button>
    </form>
    
    <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 5px;">
        <h4>🔑 Test Credentials:</h4>
        <p><strong>Admin:</strong> <EMAIL> / Admin@123</p>
        <p><strong>User:</strong> <EMAIL> / Test@123</p>
    </div>
</div>

<div style="text-align: center; margin: 20px 0;">
    <a href="reset_passwords.php" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🔧 Reset Passwords</a>
    <a href="login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🏠 Main Login</a>
    <a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🚀 Application</a>
</div>
