<?php
/**
 * Data Validation & Business Logic Testing
 * GST e-Invoice Application
 * 
 * This script tests all validation functions and business logic
 */

require_once 'config/config.php';

// Start output buffering for clean display
ob_start();

echo "<h1>Data Validation & Business Logic Testing</h1>\n";
echo "<p>Testing all validation functions and business logic rules...</p>\n";

$total_tests = 0;
$passed_tests = 0;
$failed_tests = 0;

function runTest($test_name, $test_function, $expected_result = true) {
    global $total_tests, $passed_tests, $failed_tests;
    
    $total_tests++;
    echo "<div class='test-case'>\n";
    echo "<h4>Test: {$test_name}</h4>\n";
    
    try {
        $result = $test_function();
        
        if ($result === $expected_result) {
            $passed_tests++;
            echo "<div class='alert alert-success'>✓ PASSED</div>\n";
        } else {
            $failed_tests++;
            echo "<div class='alert alert-danger'>✗ FAILED - Expected: " . var_export($expected_result, true) . ", Got: " . var_export($result, true) . "</div>\n";
        }
    } catch (Exception $e) {
        $failed_tests++;
        echo "<div class='alert alert-danger'>✗ ERROR: " . $e->getMessage() . "</div>\n";
    }
    
    echo "</div>\n";
}

// Test 1: GSTIN Validation
runTest("Valid GSTIN Format", function() {
    return validateGSTIN('27AAPFU0939F1ZV'); // Valid GSTIN
});

runTest("Invalid GSTIN Format - Wrong Length", function() {
    return validateGSTIN('27AAPFU0939F1Z'); // Too short
}, false);

runTest("Invalid GSTIN Format - Wrong Pattern", function() {
    return validateGSTIN('27AAPFU0939F1ZX'); // Wrong check digit
}, false);

runTest("Empty GSTIN", function() {
    return validateGSTIN('');
}, false);

// Test 2: HSN/SAC Code Validation
runTest("Valid HSN Code - 4 digits", function() {
    return validateHSNSAC('1234');
});

runTest("Valid HSN Code - 6 digits", function() {
    return validateHSNSAC('123456');
});

runTest("Valid HSN Code - 8 digits", function() {
    return validateHSNSAC('12345678');
});

runTest("Valid SAC Code", function() {
    return validateHSNSAC('998361');
});

runTest("Invalid HSN/SAC - Too short", function() {
    return validateHSNSAC('123');
}, false);

runTest("Invalid HSN/SAC - Too long", function() {
    return validateHSNSAC('123456789');
}, false);

runTest("Invalid HSN/SAC - Non-numeric", function() {
    return validateHSNSAC('12AB56');
}, false);

// Test 3: Email Validation
runTest("Valid Email", function() {
    return validateEmail('<EMAIL>');
});

runTest("Invalid Email - No @", function() {
    return validateEmail('testexample.com');
}, false);

runTest("Invalid Email - No domain", function() {
    return validateEmail('test@');
}, false);

runTest("Empty Email", function() {
    return validateEmail('');
}, false);

// Test 4: Tax Rate Validation
runTest("Valid Tax Rate - 0%", function() {
    return validateTaxRate(0);
});

runTest("Valid Tax Rate - 18%", function() {
    return validateTaxRate(18);
});

runTest("Valid Tax Rate - 28%", function() {
    return validateTaxRate(28);
});

runTest("Invalid Tax Rate - Negative", function() {
    return validateTaxRate(-5);
}, false);

runTest("Invalid Tax Rate - Over 100%", function() {
    return validateTaxRate(150);
}, false);

// Test 5: Amount Validation
runTest("Valid Amount - Positive", function() {
    return validateAmount(100.50);
});

runTest("Valid Amount - Zero", function() {
    return validateAmount(0);
});

runTest("Invalid Amount - Negative", function() {
    return validateAmount(-50);
}, false);

runTest("Invalid Amount - Non-numeric", function() {
    return validateAmount('abc');
}, false);

// Test 6: Invoice Number Generation
runTest("Invoice Number Generation", function() {
    $invoice_number = generateInvoiceNumber();
    return !empty($invoice_number) && strlen($invoice_number) >= 10;
});

// Test 7: Tax Calculation Logic
runTest("CGST+SGST Calculation (Same State)", function() {
    $supplier_state = 'Maharashtra';
    $recipient_state = 'Maharashtra';
    $amount = 1000;
    $tax_rate = 18;
    
    $taxes = calculateTaxes($amount, $tax_rate, $supplier_state, $recipient_state);
    
    return $taxes['cgst'] == 90 && $taxes['sgst'] == 90 && $taxes['igst'] == 0;
});

runTest("IGST Calculation (Different States)", function() {
    $supplier_state = 'Maharashtra';
    $recipient_state = 'Karnataka';
    $amount = 1000;
    $tax_rate = 18;
    
    $taxes = calculateTaxes($amount, $tax_rate, $supplier_state, $recipient_state);
    
    return $taxes['cgst'] == 0 && $taxes['sgst'] == 0 && $taxes['igst'] == 180;
});

// Test 8: Password Strength Validation
runTest("Strong Password", function() {
    return validatePasswordStrength('Admin@123');
});

runTest("Weak Password - Too Short", function() {
    return validatePasswordStrength('123');
}, false);

runTest("Weak Password - No Special Characters", function() {
    return validatePasswordStrength('Admin123');
}, false);

// Test 9: Date Validation
runTest("Valid Date Format", function() {
    return validateDate('2024-01-15');
});

runTest("Invalid Date Format", function() {
    return validateDate('15-01-2024');
}, false);

runTest("Invalid Date - Future", function() {
    $future_date = date('Y-m-d', strtotime('+1 year'));
    return validateInvoiceDate($future_date);
}, false);

// Test 10: Business Logic - Invoice Total Calculation
runTest("Invoice Total Calculation", function() {
    $items = [
        ['amount' => 1000, 'cgst_amount' => 90, 'sgst_amount' => 90, 'igst_amount' => 0],
        ['amount' => 500, 'cgst_amount' => 45, 'sgst_amount' => 45, 'igst_amount' => 0]
    ];
    
    $total = calculateInvoiceTotal($items);
    $expected_total = 1500 + 135 + 135; // Amount + CGST + SGST
    
    return abs($total - $expected_total) < 0.01;
});

// Helper functions for testing
function validateTaxRate($rate) {
    return is_numeric($rate) && $rate >= 0 && $rate <= 100;
}

function validateAmount($amount) {
    return is_numeric($amount) && $amount >= 0;
}

function generateInvoiceNumber() {
    return 'INV-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

function calculateTaxes($amount, $tax_rate, $supplier_state, $recipient_state) {
    $tax_amount = ($amount * $tax_rate) / 100;
    
    if (strtolower($supplier_state) === strtolower($recipient_state)) {
        // Same state - CGST + SGST
        return [
            'cgst' => $tax_amount / 2,
            'sgst' => $tax_amount / 2,
            'igst' => 0
        ];
    } else {
        // Different states - IGST
        return [
            'cgst' => 0,
            'sgst' => 0,
            'igst' => $tax_amount
        ];
    }
}

function validatePasswordStrength($password) {
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password) && 
           preg_match('/[^A-Za-z0-9]/', $password);
}

function validateDate($date) {
    $d = DateTime::createFromFormat('Y-m-d', $date);
    return $d && $d->format('Y-m-d') === $date;
}

function validateInvoiceDate($date) {
    $invoice_date = DateTime::createFromFormat('Y-m-d', $date);
    $today = new DateTime();
    
    return $invoice_date && $invoice_date <= $today;
}

function calculateInvoiceTotal($items) {
    $total = 0;
    foreach ($items as $item) {
        $total += $item['amount'] + $item['cgst_amount'] + $item['sgst_amount'] + $item['igst_amount'];
    }
    return $total;
}

// Test 11: Database Connection and Basic Queries
runTest("Database Connection", function() {
    try {
        $database = new Database();
        $db = $database->getConnection();
        return $db instanceof PDO;
    } catch (Exception $e) {
        return false;
    }
});

runTest("User Table Structure", function() {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $stmt = $db->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_columns = ['id', 'name', 'email', 'password', 'role', 'created_at'];
        return count(array_intersect($required_columns, $columns)) === count($required_columns);
    } catch (Exception $e) {
        return false;
    }
});

runTest("Invoice Table Structure", function() {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $stmt = $db->query("DESCRIBE invoices");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_columns = ['id', 'invoice_number', 'date', 'supplier_id', 'recipient_id', 'subtotal', 'cgst_amount', 'sgst_amount', 'igst_amount', 'total_amount'];
        return count(array_intersect($required_columns, $columns)) === count($required_columns);
    } catch (Exception $e) {
        return false;
    }
});

// Test 12: Security Functions
runTest("CSRF Token Generation", function() {
    $token = generateCSRFToken();
    return !empty($token) && strlen($token) >= 32;
});

runTest("Password Hashing", function() {
    $password = 'TestPassword123!';
    $hash = password_hash($password, PASSWORD_DEFAULT);
    return password_verify($password, $hash);
});

runTest("Input Sanitization", function() {
    $dirty_input = "<script>alert('xss')</script>";
    $clean_input = sanitizeInput($dirty_input);
    return $clean_input !== $dirty_input && !strpos($clean_input, '<script>');
});

// Display results
$content = ob_get_clean();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Validation & Business Logic Testing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-case {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .progress-bar {
            height: 25px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="summary">
            <h2>Test Summary</h2>
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary"><?php echo $total_tests; ?></h5>
                            <p class="card-text">Total Tests</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success"><?php echo $passed_tests; ?></h5>
                            <p class="card-text">Passed</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger"><?php echo $failed_tests; ?></h5>
                            <p class="card-text">Failed</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info"><?php echo round(($passed_tests / $total_tests) * 100, 1); ?>%</h5>
                            <p class="card-text">Success Rate</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <div class="progress">
                    <div class="progress-bar bg-success" style="width: <?php echo ($passed_tests / $total_tests) * 100; ?>%"></div>
                    <div class="progress-bar bg-danger" style="width: <?php echo ($failed_tests / $total_tests) * 100; ?>%"></div>
                </div>
            </div>
        </div>
        
        <?php echo $content; ?>
        
        <div class="mt-4">
            <a href="dashboard.php" class="btn btn-primary">Back to Dashboard</a>
            <a href="comprehensive_test.php" class="btn btn-secondary">Run Full System Test</a>
        </div>
    </div>
</body>
</html>
