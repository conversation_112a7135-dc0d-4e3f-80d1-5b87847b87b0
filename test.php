<?php
// Simple test file to verify P<PERSON> is working
echo "<h1>GST e-Invoice Application - System Test</h1>";
echo "<h2>PHP Version: " . phpversion() . "</h2>";
echo "<h3>Current Directory: " . __DIR__ . "</h3>";
echo "<h3>Files in Directory:</h3>";
echo "<ul>";
$files = scandir(__DIR__);
foreach($files as $file) {
    if($file != '.' && $file != '..') {
        echo "<li>" . $file . "</li>";
    }
}
echo "</ul>";

// Test database connection if config exists
if(file_exists('config/config.php')) {
    echo "<h3>Configuration File: Found</h3>";
    try {
        require_once 'config/config.php';
        echo "<h3>Database Configuration: Loaded</h3>";
        echo "<p>Database Host: " . DB_HOST . "</p>";
        echo "<p>Database Name: " . DB_NAME . "</p>";
    } catch(Exception $e) {
        echo "<h3>Configuration Error: " . $e->getMessage() . "</h3>";
    }
} else {
    echo "<h3>Configuration File: Not Found</h3>";
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>Set up MySQL database</li>";
echo "<li>Import database/schema.sql</li>";
echo "<li>Import database/test_data.sql (optional)</li>";
echo "<li>Update config/config.php with database credentials</li>";
echo "<li>Access index.php to start using the application</li>";
echo "</ol>";

echo "<hr>";
echo "<p><strong>Application Status: Ready for Database Setup</strong></p>";
?>
