<?php
/**
 * Database Installation Script
 * GST e-Invoice Application
 */

require_once '../config/config.php';

// Check if already installed
if (file_exists('../config/.installed')) {
    die('Application is already installed. Delete config/.installed file to reinstall.');
}

$errors = [];
$success = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'gst_einvoice';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    $admin_name = $_POST['admin_name'] ?? 'Admin User';
    $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
    $admin_password = $_POST['admin_password'] ?? '';
    
    // Validate inputs
    if (empty($admin_password) || strlen($admin_password) < 6) {
        $errors[] = 'Admin password must be at least 6 characters long.';
    }
    
    if (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid admin email address.';
    }
    
    if (empty($errors)) {
        try {
            // Connect to MySQL server
            $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$db_name`");
            
            // Read and execute schema
            $schema = file_get_contents('schema.sql');
            
            // Remove the database creation and use statements from schema
            $schema = preg_replace('/CREATE DATABASE.*?;/i', '', $schema);
            $schema = preg_replace('/USE.*?;/i', '', $schema);
            
            // Split into individual statements
            $statements = array_filter(array_map('trim', explode(';', $schema)));
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^(--|\/\*)/i', $statement)) {
                    $pdo->exec($statement);
                }
            }
            
            // Update admin user with provided details
            $hashedPassword = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ?, password = ? WHERE role = 'admin' LIMIT 1");
            $stmt->execute([$admin_name, $admin_email, $hashedPassword]);
            
            // Update database configuration
            $config_content = "<?php
/**
 * Database Configuration
 * GST e-Invoice Application
 */

class Database {
    private \$host = '$db_host';
    private \$db_name = '$db_name';
    private \$username = '$db_user';
    private \$password = '$db_pass';
    private \$conn;

    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$this->conn = new PDO(
                \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name,
                \$this->username,
                \$this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8\"
                )
            );
        } catch(PDOException \$exception) {
            echo \"Connection error: \" . \$exception->getMessage();
        }
        
        return \$this->conn;
    }
}
?>";
            
            file_put_contents('../config/database.php', $config_content);
            
            // Create installation marker
            file_put_contents('../config/.installed', date('Y-m-d H:i:s'));
            
            $success[] = 'Database installed successfully!';
            $success[] = "Admin login: $admin_email";
            $success[] = 'You can now login to the application.';
            
        } catch (PDOException $e) {
            $errors[] = 'Database error: ' . $e->getMessage();
        } catch (Exception $e) {
            $errors[] = 'Installation error: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install - GST e-Invoice System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            GST e-Invoice System Installation
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h6>Installation Errors:</h6>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success">
                                <h6>Installation Successful!</h6>
                                <ul class="mb-0">
                                    <?php foreach ($success as $msg): ?>
                                        <li><?php echo htmlspecialchars($msg); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                                <hr>
                                <a href="../login.php" class="btn btn-success">
                                    <i class="fas fa-sign-in-alt me-1"></i>
                                    Go to Login
                                </a>
                            </div>
                        <?php else: ?>
                            <form method="POST">
                                <h5 class="mb-3">Database Configuration</h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="db_host" class="form-label">Database Host</label>
                                            <input type="text" class="form-control" id="db_host" name="db_host" 
                                                   value="<?php echo $_POST['db_host'] ?? 'localhost'; ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="db_name" class="form-label">Database Name</label>
                                            <input type="text" class="form-control" id="db_name" name="db_name" 
                                                   value="<?php echo $_POST['db_name'] ?? 'gst_einvoice'; ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="db_user" class="form-label">Database Username</label>
                                            <input type="text" class="form-control" id="db_user" name="db_user" 
                                                   value="<?php echo $_POST['db_user'] ?? 'root'; ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="db_pass" class="form-label">Database Password</label>
                                            <input type="password" class="form-control" id="db_pass" name="db_pass" 
                                                   value="<?php echo $_POST['db_pass'] ?? ''; ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <hr>
                                <h5 class="mb-3">Admin User Configuration</h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="admin_name" class="form-label">Admin Name</label>
                                            <input type="text" class="form-control" id="admin_name" name="admin_name" 
                                                   value="<?php echo $_POST['admin_name'] ?? 'Admin User'; ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="admin_email" class="form-label">Admin Email</label>
                                            <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                                   value="<?php echo $_POST['admin_email'] ?? '<EMAIL>'; ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="admin_password" class="form-label">Admin Password</label>
                                    <input type="password" class="form-control" id="admin_password" name="admin_password" 
                                           minlength="6" required>
                                    <div class="form-text">Password must be at least 6 characters long.</div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-download me-2"></i>
                                        Install Database
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
