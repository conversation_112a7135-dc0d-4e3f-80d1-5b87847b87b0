<?php
/**
 * Product Master Management
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login
requireLogin();

$page_title = 'Product Master';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!validateCSRFToken($csrf_token)) {
            $_SESSION['error_message'] = 'Invalid request. Please try again.';
        } else {
            switch ($action) {
                case 'create':
                    $name = sanitizeInput($_POST['name'] ?? '');
                    $description = sanitizeInput($_POST['description'] ?? '');
                    $hsn_sac = sanitizeInput($_POST['hsn_sac'] ?? '');
                    $unit = sanitizeInput($_POST['unit'] ?? '');
                    $rate = (float)($_POST['rate'] ?? 0);
                    $gst_rate = (float)($_POST['gst_rate'] ?? 0);
                    
                    if (empty($name) || empty($hsn_sac) || empty($unit)) {
                        $_SESSION['error_message'] = 'Name, HSN/SAC code, and unit are required.';
                    } elseif (!preg_match('/^[0-9]{4,8}$/', $hsn_sac)) {
                        $_SESSION['error_message'] = 'HSN/SAC code must be 4-8 digits.';
                    } elseif ($rate < 0) {
                        $_SESSION['error_message'] = 'Rate cannot be negative.';
                    } elseif ($gst_rate < 0 || $gst_rate > 100) {
                        $_SESSION['error_message'] = 'GST rate must be between 0 and 100.';
                    } else {
                        $stmt = $db->prepare("
                            INSERT INTO products (name, description, hsn_sac, unit, rate, gst_rate, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ");
                        
                        if ($stmt->execute([$name, $description, $hsn_sac, $unit, $rate, $gst_rate, $_SESSION['user_id']])) {
                            $_SESSION['success_message'] = 'Product created successfully.';
                            
                            // Log product creation
                            $product_id = $db->lastInsertId();
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                VALUES (?, 'products', ?, 'INSERT', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $product_id, 
                                json_encode(['name' => $name, 'hsn_sac' => $hsn_sac]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to create product.';
                        }
                    }
                    break;
                    
                case 'update':
                    $product_id = (int)($_POST['product_id'] ?? 0);
                    $name = sanitizeInput($_POST['name'] ?? '');
                    $description = sanitizeInput($_POST['description'] ?? '');
                    $hsn_sac = sanitizeInput($_POST['hsn_sac'] ?? '');
                    $unit = sanitizeInput($_POST['unit'] ?? '');
                    $rate = (float)($_POST['rate'] ?? 0);
                    $gst_rate = (float)($_POST['gst_rate'] ?? 0);
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    if (empty($name) || empty($hsn_sac) || empty($unit)) {
                        $_SESSION['error_message'] = 'Name, HSN/SAC code, and unit are required.';
                    } elseif (!preg_match('/^[0-9]{4,8}$/', $hsn_sac)) {
                        $_SESSION['error_message'] = 'HSN/SAC code must be 4-8 digits.';
                    } elseif ($rate < 0) {
                        $_SESSION['error_message'] = 'Rate cannot be negative.';
                    } elseif ($gst_rate < 0 || $gst_rate > 100) {
                        $_SESSION['error_message'] = 'GST rate must be between 0 and 100.';
                    } else {
                        $stmt = $db->prepare("
                            UPDATE products 
                            SET name = ?, description = ?, hsn_sac = ?, unit = ?, rate = ?, gst_rate = ?, is_active = ? 
                            WHERE id = ?
                        ");
                        
                        if ($stmt->execute([$name, $description, $hsn_sac, $unit, $rate, $gst_rate, $is_active, $product_id])) {
                            $_SESSION['success_message'] = 'Product updated successfully.';
                            
                            // Log product update
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                VALUES (?, 'products', ?, 'UPDATE', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $product_id, 
                                json_encode(['name' => $name, 'hsn_sac' => $hsn_sac, 'is_active' => $is_active]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to update product.';
                        }
                    }
                    break;
                    
                case 'delete':
                    $product_id = (int)($_POST['product_id'] ?? 0);
                    
                    // Check if product has invoice items
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoice_items WHERE product_id = ?");
                    $stmt->execute([$product_id]);
                    $item_count = $stmt->fetch()['count'];
                    
                    if ($item_count > 0) {
                        $_SESSION['error_message'] = 'Cannot delete product with existing invoice items. Deactivate instead.';
                    } else {
                        $stmt = $db->prepare("DELETE FROM products WHERE id = ?");
                        
                        if ($stmt->execute([$product_id])) {
                            $_SESSION['success_message'] = 'Product deleted successfully.';
                            
                            // Log product deletion
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, old_values, ip_address, user_agent) 
                                VALUES (?, 'products', ?, 'DELETE', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $product_id, 
                                json_encode(['deleted_by' => $_SESSION['user_name']]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to delete product.';
                        }
                    }
                    break;
            }
        }
        
        header('Location: products.php');
        exit();
    }
    
    // Handle CSV import
    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
        $csv_file = $_FILES['csv_file']['tmp_name'];
        $imported = 0;
        $errors = [];
        
        if (($handle = fopen($csv_file, 'r')) !== FALSE) {
            $header = fgetcsv($handle); // Skip header row
            
            while (($data = fgetcsv($handle)) !== FALSE) {
                if (count($data) >= 4) { // Minimum required fields
                    $name = sanitizeInput($data[0] ?? '');
                    $description = sanitizeInput($data[1] ?? '');
                    $hsn_sac = sanitizeInput($data[2] ?? '');
                    $unit = sanitizeInput($data[3] ?? '');
                    $rate = (float)($data[4] ?? 0);
                    $gst_rate = (float)($data[5] ?? 0);
                    
                    if (!empty($name) && !empty($hsn_sac) && !empty($unit)) {
                        // Validate HSN/SAC
                        if (!preg_match('/^[0-9]{4,8}$/', $hsn_sac)) {
                            $errors[] = "Invalid HSN/SAC for $name: $hsn_sac";
                            continue;
                        }
                        
                        $stmt = $db->prepare("
                            INSERT INTO products (name, description, hsn_sac, unit, rate, gst_rate, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ");
                        
                        if ($stmt->execute([$name, $description, $hsn_sac, $unit, $rate, $gst_rate, $_SESSION['user_id']])) {
                            $imported++;
                        } else {
                            $errors[] = "Failed to import $name";
                        }
                    }
                }
            }
            fclose($handle);
        }
        
        if ($imported > 0) {
            $_SESSION['success_message'] = "$imported products imported successfully.";
        }
        
        if (!empty($errors)) {
            $_SESSION['error_message'] = implode('<br>', $errors);
        }
        
        header('Location: products.php');
        exit();
    }
    
    // Get all products with filtering
    $search = $_GET['search'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(p.name LIKE ? OR p.hsn_sac LIKE ? OR p.description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($status_filter !== '') {
        $where_conditions[] = "p.is_active = ?";
        $params[] = (int)$status_filter;
    }
    
    // Add user restriction for non-admin users
    if (!isAdmin()) {
        $where_conditions[] = "p.created_by = ?";
        $params[] = $_SESSION['user_id'];
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $stmt = $db->prepare("
        SELECT p.*, 
               u.name as created_by_name,
               COUNT(ii.id) as usage_count
        FROM products p
        LEFT JOIN users u ON p.created_by = u.id
        LEFT JOIN invoice_items ii ON p.id = ii.product_id
        $where_clause
        GROUP BY p.id
        ORDER BY p.created_at DESC
    ");
    $stmt->execute($params);
    $products = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log('Product management error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while loading products.';
    $products = [];
}

include '../../includes/header.php';
?>
