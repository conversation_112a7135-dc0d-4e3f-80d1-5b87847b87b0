<?php
/**
 * Session Test Utility
 * Test session management and security functions
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔐 Session & Security Test Utility</h2>";
echo "<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
.success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 10px; }
button { background: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
button:hover { background: #0056b3; }
.logout-btn { background: #dc3545; }
.logout-btn:hover { background: #c82333; }
</style>";

// Include config
require_once 'config/config.php';

echo "<div class='section'>";
echo "<h3>📊 Session Information</h3>";

// Session status
$session_status = session_status();
$status_text = [
    PHP_SESSION_DISABLED => 'Disabled',
    PHP_SESSION_NONE => 'None',
    PHP_SESSION_ACTIVE => 'Active'
];

echo "<div class='info'>Session Status: " . $status_text[$session_status] . "</div>";
echo "<div class='info'>Session ID: " . session_id() . "</div>";
echo "<div class='info'>Session Name: " . session_name() . "</div>";

// Session configuration
echo "<h4>Session Configuration:</h4>";
echo "<div class='info'>Session Timeout: " . SESSION_TIMEOUT . " seconds (" . (SESSION_TIMEOUT/60) . " minutes)</div>";
echo "<div class='info'>Max Login Attempts: " . MAX_LOGIN_ATTEMPTS . "</div>";
echo "<div class='info'>Lockout Duration: " . LOCKOUT_DURATION . " seconds (" . (LOCKOUT_DURATION/60) . " minutes)</div>";
echo "<div class='info'>Base URL: " . BASE_URL . "</div>";

echo "</div>";

echo "<div class='section'>";
echo "<h3>🔑 Current Session Data</h3>";

if (empty($_SESSION)) {
    echo "<div class='warning'>⚠️ No session data found</div>";
} else {
    echo "<div class='success'>✅ Session data exists</div>";
    foreach ($_SESSION as $key => $value) {
        if ($key === 'csrf_token') {
            echo "<div class='info'>$key: " . substr($value, 0, 20) . "...</div>";
        } else {
            echo "<div class='info'>$key: " . htmlspecialchars($value) . "</div>";
        }
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h3>👤 Login Status</h3>";

if (isLoggedIn()) {
    echo "<div class='success'>✅ User is logged in</div>";
    echo "<div class='info'>User ID: " . $_SESSION['user_id'] . "</div>";
    echo "<div class='info'>User Name: " . $_SESSION['user_name'] . "</div>";
    echo "<div class='info'>User Email: " . $_SESSION['user_email'] . "</div>";
    echo "<div class='info'>User Role: " . $_SESSION['user_role'] . "</div>";
    
    if (isset($_SESSION['login_time'])) {
        $login_time = $_SESSION['login_time'];
        $current_time = time();
        $session_age = $current_time - $login_time;
        $remaining_time = SESSION_TIMEOUT - $session_age;
        
        echo "<div class='info'>Login Time: " . date('Y-m-d H:i:s', $login_time) . "</div>";
        echo "<div class='info'>Session Age: " . $session_age . " seconds</div>";
        echo "<div class='info'>Remaining Time: " . max(0, $remaining_time) . " seconds</div>";
        
        if ($remaining_time <= 0) {
            echo "<div class='error'>❌ Session has expired</div>";
        } else {
            echo "<div class='success'>✅ Session is valid</div>";
        }
    }
    
    echo "<div class='info'>Is Admin: " . (isAdmin() ? 'Yes' : 'No') . "</div>";
    
    echo "<p><a href='dashboard.php'><button>📊 Go to Dashboard</button></a></p>";
    echo "<p><a href='modules/auth/logout.php'><button class='logout-btn'>🚪 Logout</button></a></p>";
    
} else {
    echo "<div class='warning'>⚠️ User is not logged in</div>";
    echo "<p><a href='login.php'><button>🔐 Go to Login</button></a></p>";
    echo "<p><a href='test_login.php'><button>🧪 Test Login</button></a></p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h3>🛡️ Security Functions Test</h3>";

// Test CSRF token
try {
    $csrf_token = generateCSRFToken();
    echo "<div class='success'>✅ CSRF token generated: " . substr($csrf_token, 0, 20) . "...</div>";
    
    // Test validation
    $is_valid = validateCSRFToken($csrf_token);
    echo "<div class='" . ($is_valid ? 'success' : 'error') . "'>";
    echo ($is_valid ? '✅' : '❌') . " CSRF token validation: " . ($is_valid ? 'PASS' : 'FAIL');
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ CSRF token error: " . $e->getMessage() . "</div>";
}

// Test input sanitization
$test_input = "<script>alert('test')</script>";
$sanitized = sanitizeInput($test_input);
echo "<div class='info'>Input sanitization test:</div>";
echo "<div class='info'>Original: " . htmlspecialchars($test_input) . "</div>";
echo "<div class='info'>Sanitized: " . $sanitized . "</div>";
echo "<div class='" . ($sanitized !== $test_input ? 'success' : 'error') . "'>";
echo ($sanitized !== $test_input ? '✅' : '❌') . " Sanitization working";
echo "</div>";

echo "</div>";

echo "<div class='section'>";
echo "<h3>🔧 Session Actions</h3>";

// Handle session actions
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'regenerate':
            session_regenerate_id(true);
            echo "<div class='success'>✅ Session ID regenerated</div>";
            echo "<div class='info'>New Session ID: " . session_id() . "</div>";
            break;
            
        case 'clear':
            session_unset();
            echo "<div class='success'>✅ Session data cleared</div>";
            break;
            
        case 'destroy':
            session_destroy();
            session_start(); // Restart for this page
            echo "<div class='success'>✅ Session destroyed and restarted</div>";
            break;
    }
    
    echo "<script>
    setTimeout(function() {
        window.location.href = 'test_session.php';
    }, 2000);
    </script>";
}

echo "<p>";
echo "<a href='?action=regenerate'><button>🔄 Regenerate Session ID</button></a>";
echo "<a href='?action=clear'><button>🧹 Clear Session Data</button></a>";
echo "<a href='?action=destroy'><button class='logout-btn'>💥 Destroy Session</button></a>";
echo "</p>";

echo "</div>";

echo "<div class='section'>";
echo "<h3>🌐 Environment Information</h3>";

echo "<div class='info'>PHP Version: " . PHP_VERSION . "</div>";
echo "<div class='info'>Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</div>";
echo "<div class='info'>Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</div>";
echo "<div class='info'>Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "</div>";
echo "<div class='info'>HTTP Host: " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</div>";
echo "<div class='info'>Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</div>";
echo "<div class='info'>User Agent: " . substr($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown', 0, 100) . "...</div>";

echo "</div>";
?>

<div style="text-align: center; margin: 20px 0;">
    <a href="reset_passwords.php" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🔧 Reset Passwords</a>
    <a href="test_login.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🧪 Test Login</a>
    <a href="test_database.php" style="background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🗄️ Test Database</a>
    <a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🚀 Application</a>
</div>
