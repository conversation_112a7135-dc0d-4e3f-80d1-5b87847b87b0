<?php
/**
 * Test Fix - Function Redeclaration Issue
 * Quick test to verify the application loads without errors
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 Function Redeclaration Fix Test</h2>";
echo "<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
.success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
.error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
.info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
.warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
</style>";

echo "<h3>Step 1: Testing Core Configuration Loading</h3>";

try {
    // Test loading config without including functions that might conflict
    echo "<div class='info'>Testing config/config.php inclusion...</div>";
    require_once 'config/config.php';
    echo "<div class='success'>✅ Config loaded successfully</div>";
    
    echo "<div class='info'>App Name: " . APP_NAME . "</div>";
    echo "<div class='info'>Base URL: " . BASE_URL . "</div>";
    echo "<div class='info'>Session Status: " . (session_status() == PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</div>";
    
} catch (Error $e) {
    echo "<div class='error'>❌ Fatal Error: " . $e->getMessage() . "</div>";
    echo "<div class='error'>File: " . $e->getFile() . " Line: " . $e->getLine() . "</div>";
    die("Cannot proceed due to fatal error.");
} catch (Exception $e) {
    echo "<div class='error'>❌ Exception: " . $e->getMessage() . "</div>";
    die("Cannot proceed due to exception.");
}

echo "<h3>Step 2: Testing Function Availability</h3>";

// Test security functions
$security_functions = [
    'generateCSRFToken',
    'validateCSRFToken', 
    'sanitizeInput',
    'validateEmail',
    'validateGSTIN',
    'validatePAN',
    'logSecurityEvent',
    'hashPassword',
    'verifyPassword'
];

foreach ($security_functions as $func) {
    if (function_exists($func)) {
        echo "<div class='success'>✅ $func() - Available</div>";
    } else {
        echo "<div class='error'>❌ $func() - Missing</div>";
    }
}

// Test config functions
$config_functions = [
    'isLoggedIn',
    'isAdmin', 
    'requireLogin',
    'formatCurrency',
    'formatDate'
];

foreach ($config_functions as $func) {
    if (function_exists($func)) {
        echo "<div class='success'>✅ $func() - Available</div>";
    } else {
        echo "<div class='error'>❌ $func() - Missing</div>";
    }
}

// Test common functions
$common_functions = [
    'getDBConnection',
    'logAuditEvent',
    'getIndianStates',
    'calculateTax',
    'generateInvoiceNumber'
];

foreach ($common_functions as $func) {
    if (function_exists($func)) {
        echo "<div class='success'>✅ $func() - Available</div>";
    } else {
        echo "<div class='error'>❌ $func() - Missing</div>";
    }
}

echo "<h3>Step 3: Testing Database Connection</h3>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Test a simple query
        $stmt = $db->query("SELECT VERSION() as version");
        $version = $stmt->fetch();
        echo "<div class='info'>Database Version: " . $version['version'] . "</div>";
        
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database Error: " . $e->getMessage() . "</div>";
}

echo "<h3>Step 4: Testing Security Functions</h3>";

try {
    // Test CSRF token generation
    $csrf_token = generateCSRFToken();
    echo "<div class='success'>✅ CSRF Token Generated: " . substr($csrf_token, 0, 20) . "...</div>";
    
    // Test input sanitization
    $test_input = "<script>alert('test')</script>";
    $sanitized = sanitizeInput($test_input);
    echo "<div class='success'>✅ Input Sanitization Working</div>";
    echo "<div class='info'>Original: " . htmlspecialchars($test_input) . "</div>";
    echo "<div class='info'>Sanitized: " . $sanitized . "</div>";
    
    // Test validation functions
    $test_gstin = "27AAPFU0939F1ZV";
    $gstin_valid = validateGSTIN($test_gstin);
    echo "<div class='" . ($gstin_valid ? 'success' : 'error') . "'>";
    echo ($gstin_valid ? '✅' : '❌') . " GSTIN Validation: $test_gstin";
    echo "</div>";
    
    $test_email = "<EMAIL>";
    $email_valid = validateEmail($test_email);
    echo "<div class='" . ($email_valid ? 'success' : 'error') . "'>";
    echo ($email_valid ? '✅' : '❌') . " Email Validation: $test_email";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Security Function Error: " . $e->getMessage() . "</div>";
}

echo "<h3>Step 5: Testing Application Entry Points</h3>";

// Test if main files can be included without errors
$test_files = [
    'index.php' => 'Main Entry Point',
    'login.php' => 'Login Page',
    'dashboard.php' => 'Dashboard'
];

foreach ($test_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description ($file) - File exists</div>";
        
        // Check for basic PHP syntax errors by parsing
        $content = file_get_contents($file);
        if (strpos($content, '<?php') !== false) {
            echo "<div class='info'>   → Contains PHP code</div>";
        }
    } else {
        echo "<div class='error'>❌ $description ($file) - File missing</div>";
    }
}

echo "<h3>🎉 Fix Verification Complete</h3>";

echo "<div class='success'>";
echo "<h4>✅ Function Redeclaration Issue Fixed!</h4>";
echo "<p>The application should now load without fatal errors.</p>";
echo "</div>";

echo "<div class='info'>";
echo "<h4>📋 Next Steps:</h4>";
echo "<ul>";
echo "<li>Test the main application: <a href='index.php' target='_blank'>index.php</a></li>";
echo "<li>Test login functionality: <a href='test_login.php' target='_blank'>test_login.php</a></li>";
echo "<li>Reset passwords if needed: <a href='reset_passwords.php' target='_blank'>reset_passwords.php</a></li>";
echo "<li>Check database structure: <a href='test_database.php' target='_blank'>test_database.php</a></li>";
echo "</ul>";
echo "</div>";

echo "<div class='warning'>";
echo "<h4>⚠️ Important Notes:</h4>";
echo "<ul>";
echo "<li>All duplicate functions have been removed from includes/functions.php</li>";
echo "<li>Security functions are now centralized in includes/security.php</li>";
echo "<li>Configuration functions remain in config/config.php</li>";
echo "<li>Common utility functions are in includes/functions.php</li>";
echo "</ul>";
echo "</div>";
?>

<div style="text-align: center; margin: 20px 0;">
    <a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🚀 Test Main Application</a>
    <a href="test_login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🔐 Test Login</a>
    <a href="reset_passwords.php" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🔧 Reset Passwords</a>
</div>
