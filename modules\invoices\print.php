<?php
/**
 * Invoice Print View with QR Code
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login
requireLogin();

// Get invoice ID
$invoice_id = (int)($_GET['id'] ?? 0);

if (!$invoice_id) {
    $_SESSION['error_message'] = 'Invalid invoice ID.';
    header('Location: list.php');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get invoice with related data
    $stmt = $db->prepare("
        SELECT i.*, 
               s.name as supplier_name, s.address as supplier_address, s.state as supplier_state, 
               s.gstin as supplier_gstin, s.contact_person as supplier_contact, s.email as supplier_email, s.phone as supplier_phone,
               r.name as recipient_name, r.address as recipient_address, r.state as recipient_state, 
               r.gstin as recipient_gstin, r.contact_person as recipient_contact, r.email as recipient_email, 
               r.phone as recipient_phone, r.pincode as recipient_pincode,
               u.name as created_by_name
        FROM invoices i
        JOIN suppliers s ON i.supplier_id = s.id
        JOIN recipients r ON i.recipient_id = r.id
        JOIN users u ON i.user_id = u.id
        WHERE i.id = ?
    ");
    $stmt->execute([$invoice_id]);
    $invoice = $stmt->fetch();
    
    if (!$invoice) {
        $_SESSION['error_message'] = 'Invoice not found.';
        header('Location: list.php');
        exit();
    }
    
    // Check access permissions
    if (!isAdmin() && $invoice['user_id'] != $_SESSION['user_id']) {
        $_SESSION['error_message'] = 'Access denied.';
        header('Location: list.php');
        exit();
    }
    
    // Get invoice items
    $stmt = $db->prepare("
        SELECT ii.*, p.name as product_name
        FROM invoice_items ii
        LEFT JOIN products p ON ii.product_id = p.id
        WHERE ii.invoice_id = ?
        ORDER BY ii.id
    ");
    $stmt->execute([$invoice_id]);
    $items = $stmt->fetchAll();
    
    // Generate QR code data (simplified version for demo)
    $qr_data = json_encode([
        'invoice_number' => $invoice['invoice_number'],
        'date' => $invoice['date'],
        'supplier_gstin' => $invoice['supplier_gstin'],
        'recipient_gstin' => $invoice['recipient_gstin'],
        'total_amount' => $invoice['total_amount']
    ]);
    
    // For production, you would use a proper QR code library like endroid/qr-code
    // For now, we'll use a simple online QR code generator
    $qr_code_url = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' . urlencode($qr_data);
    
} catch (Exception $e) {
    error_log('Invoice print error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while loading the invoice.';
    header('Location: list.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice <?php echo htmlspecialchars($invoice['invoice_number']); ?> - Print</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .print-break { page-break-after: always; }
            body { font-size: 12px; }
            .table { font-size: 11px; }
        }
        
        .invoice-header {
            border-bottom: 2px solid #007bff;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }
        
        .company-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .invoice-details {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
        }
        
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        
        .tax-summary {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .total-amount {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .signature-section {
            margin-top: 50px;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Print Controls -->
        <div class="row no-print mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h4>Invoice Print Preview</h4>
                    <div>
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print me-2"></i>
                            Print Invoice
                        </button>
                        <a href="view.php?id=<?php echo $invoice_id; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Invoice
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Invoice Content -->
        <div class="row">
            <div class="col-12">
                <!-- Header -->
                <div class="invoice-header">
                    <div class="row">
                        <div class="col-md-8">
                            <h2 class="text-primary mb-0">TAX INVOICE</h2>
                            <p class="text-muted mb-0">GST e-Invoice Compliant</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="qr-code">
                                <img src="<?php echo htmlspecialchars($qr_code_url); ?>" alt="QR Code" class="img-fluid">
                                <p class="small text-muted mt-2">Scan for verification</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Invoice Details -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="invoice-details">
                            <h6 class="text-primary mb-2">Invoice Details</h6>
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td><strong>Invoice No:</strong></td>
                                    <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Date:</strong></td>
                                    <td><?php echo date('d/m/Y', strtotime($invoice['date'])); ?></td>
                                </tr>
                                <?php if ($invoice['due_date']): ?>
                                <tr>
                                    <td><strong>Due Date:</strong></td>
                                    <td><?php echo date('d/m/Y', strtotime($invoice['due_date'])); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td><strong>Place of Supply:</strong></td>
                                    <td><?php echo htmlspecialchars($invoice['place_of_supply']); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="invoice-details">
                            <h6 class="text-primary mb-2">Status</h6>
                            <span class="badge bg-<?php 
                                echo $invoice['status'] === 'paid' ? 'success' : 
                                    ($invoice['status'] === 'sent' ? 'info' : 'warning'); 
                            ?> fs-6">
                                <?php echo strtoupper($invoice['status']); ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Supplier and Recipient Details -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="company-details">
                            <h6 class="text-primary mb-3">Supplier Details</h6>
                            <h5 class="mb-2"><?php echo htmlspecialchars($invoice['supplier_name']); ?></h5>
                            <?php if ($invoice['supplier_address']): ?>
                                <p class="mb-1"><?php echo nl2br(htmlspecialchars($invoice['supplier_address'])); ?></p>
                            <?php endif; ?>
                            <p class="mb-1"><strong>State:</strong> <?php echo htmlspecialchars($invoice['supplier_state']); ?></p>
                            <?php if ($invoice['supplier_gstin']): ?>
                                <p class="mb-1"><strong>GSTIN:</strong> <?php echo htmlspecialchars($invoice['supplier_gstin']); ?></p>
                            <?php endif; ?>
                            <?php if ($invoice['supplier_contact']): ?>
                                <p class="mb-1"><strong>Contact:</strong> <?php echo htmlspecialchars($invoice['supplier_contact']); ?></p>
                            <?php endif; ?>
                            <?php if ($invoice['supplier_phone']): ?>
                                <p class="mb-1"><strong>Phone:</strong> <?php echo htmlspecialchars($invoice['supplier_phone']); ?></p>
                            <?php endif; ?>
                            <?php if ($invoice['supplier_email']): ?>
                                <p class="mb-0"><strong>Email:</strong> <?php echo htmlspecialchars($invoice['supplier_email']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="company-details">
                            <h6 class="text-primary mb-3">Bill To</h6>
                            <h5 class="mb-2"><?php echo htmlspecialchars($invoice['recipient_name']); ?></h5>
                            <?php if ($invoice['recipient_address']): ?>
                                <p class="mb-1"><?php echo nl2br(htmlspecialchars($invoice['recipient_address'])); ?></p>
                            <?php endif; ?>
                            <p class="mb-1"><strong>State:</strong> <?php echo htmlspecialchars($invoice['recipient_state']); ?></p>
                            <?php if ($invoice['recipient_pincode']): ?>
                                <p class="mb-1"><strong>Pincode:</strong> <?php echo htmlspecialchars($invoice['recipient_pincode']); ?></p>
                            <?php endif; ?>
                            <?php if ($invoice['recipient_gstin']): ?>
                                <p class="mb-1"><strong>GSTIN:</strong> <?php echo htmlspecialchars($invoice['recipient_gstin']); ?></p>
                            <?php endif; ?>
                            <?php if ($invoice['recipient_contact']): ?>
                                <p class="mb-1"><strong>Contact:</strong> <?php echo htmlspecialchars($invoice['recipient_contact']); ?></p>
                            <?php endif; ?>
                            <?php if ($invoice['recipient_phone']): ?>
                                <p class="mb-1"><strong>Phone:</strong> <?php echo htmlspecialchars($invoice['recipient_phone']); ?></p>
                            <?php endif; ?>
                            <?php if ($invoice['recipient_email']): ?>
                                <p class="mb-0"><strong>Email:</strong> <?php echo htmlspecialchars($invoice['recipient_email']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Items Table -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">Invoice Items</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-primary">
                                    <tr>
                                        <th>S.No</th>
                                        <th>Description</th>
                                        <th>HSN/SAC</th>
                                        <th>Qty</th>
                                        <th>Unit</th>
                                        <th>Rate</th>
                                        <th>Amount</th>
                                        <th>GST%</th>
                                        <th>CGST</th>
                                        <th>SGST</th>
                                        <th>IGST</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $sl_no = 1;
                                    foreach ($items as $item): 
                                        $item_total = $item['amount'] + $item['cgst_amount'] + $item['sgst_amount'] + $item['igst_amount'];
                                    ?>
                                    <tr>
                                        <td><?php echo $sl_no++; ?></td>
                                        <td><?php echo htmlspecialchars($item['description']); ?></td>
                                        <td><?php echo htmlspecialchars($item['hsn_sac']); ?></td>
                                        <td class="text-end"><?php echo number_format($item['quantity'], 2); ?></td>
                                        <td><?php echo htmlspecialchars($item['unit']); ?></td>
                                        <td class="text-end">₹<?php echo number_format($item['rate'], 2); ?></td>
                                        <td class="text-end">₹<?php echo number_format($item['amount'], 2); ?></td>
                                        <td class="text-center"><?php echo number_format($item['gst_rate'], 1); ?>%</td>
                                        <td class="text-end">₹<?php echo number_format($item['cgst_amount'], 2); ?></td>
                                        <td class="text-end">₹<?php echo number_format($item['sgst_amount'], 2); ?></td>
                                        <td class="text-end">₹<?php echo number_format($item['igst_amount'], 2); ?></td>
                                        <td class="text-end"><strong>₹<?php echo number_format($item_total, 2); ?></strong></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
