<?php
/**
 * Bulk Operations Module
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login and admin access
requireLogin();
if (!isAdmin()) {
    $_SESSION['error_message'] = 'Access denied. Admin privileges required.';
    header('Location: ' . BASE_URL . 'dashboard.php');
    exit();
}

$page_title = 'Bulk Operations';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error_message'] = 'Invalid security token.';
        header('Location: index.php');
        exit();
    }
    
    $operation = $_POST['operation'] ?? '';
    
    switch ($operation) {
        case 'import_suppliers':
            handleSupplierImport();
            break;
        case 'import_recipients':
            handleRecipientImport();
            break;
        case 'import_products':
            handleProductImport();
            break;
        case 'bulk_delete':
            handleBulkDelete();
            break;
        case 'bulk_update':
            handleBulkUpdate();
            break;
        case 'send_notifications':
            handleBulkNotifications();
            break;
    }
}

function handleSupplierImport() {
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        $_SESSION['error_message'] = 'Please select a valid CSV file.';
        return;
    }
    
    $file = $_FILES['csv_file']['tmp_name'];
    $results = importCSVData($file, 'suppliers');
    
    if ($results['success'] > 0) {
        $_SESSION['success_message'] = "Successfully imported {$results['success']} suppliers. {$results['errors']} errors.";
    } else {
        $_SESSION['error_message'] = "Import failed. {$results['errors']} errors found.";
    }
}

function handleRecipientImport() {
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        $_SESSION['error_message'] = 'Please select a valid CSV file.';
        return;
    }
    
    $file = $_FILES['csv_file']['tmp_name'];
    $results = importCSVData($file, 'recipients');
    
    if ($results['success'] > 0) {
        $_SESSION['success_message'] = "Successfully imported {$results['success']} recipients. {$results['errors']} errors.";
    } else {
        $_SESSION['error_message'] = "Import failed. {$results['errors']} errors found.";
    }
}

function handleProductImport() {
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        $_SESSION['error_message'] = 'Please select a valid CSV file.';
        return;
    }
    
    $file = $_FILES['csv_file']['tmp_name'];
    $results = importCSVData($file, 'products');
    
    if ($results['success'] > 0) {
        $_SESSION['success_message'] = "Successfully imported {$results['success']} products. {$results['errors']} errors.";
    } else {
        $_SESSION['error_message'] = "Import failed. {$results['errors']} errors found.";
    }
}

function handleBulkDelete() {
    $table = $_POST['table'] ?? '';
    $ids = $_POST['ids'] ?? [];
    
    if (empty($table) || empty($ids) || !is_array($ids)) {
        $_SESSION['error_message'] = 'Invalid delete request.';
        return;
    }
    
    $allowed_tables = ['suppliers', 'recipients', 'products'];
    if (!in_array($table, $allowed_tables)) {
        $_SESSION['error_message'] = 'Invalid table specified.';
        return;
    }
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $stmt = $db->prepare("DELETE FROM {$table} WHERE id IN ({$placeholders})");
        $stmt->execute($ids);
        
        $deleted_count = $stmt->rowCount();
        $_SESSION['success_message'] = "Successfully deleted {$deleted_count} records from {$table}.";
        
        // Log bulk delete
        logAuditEvent($table, 0, 'BULK_DELETE', null, ['deleted_ids' => $ids, 'count' => $deleted_count]);
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Bulk delete failed: ' . $e->getMessage();
        error_log('Bulk delete error: ' . $e->getMessage());
    }
}

function handleBulkUpdate() {
    $table = $_POST['table'] ?? '';
    $field = $_POST['field'] ?? '';
    $value = $_POST['value'] ?? '';
    $ids = $_POST['ids'] ?? [];
    
    if (empty($table) || empty($field) || empty($ids) || !is_array($ids)) {
        $_SESSION['error_message'] = 'Invalid update request.';
        return;
    }
    
    $allowed_tables = ['suppliers', 'recipients', 'products'];
    $allowed_fields = [
        'suppliers' => ['state', 'status'],
        'recipients' => ['state', 'status'],
        'products' => ['tax_rate', 'unit', 'status']
    ];
    
    if (!in_array($table, $allowed_tables) || !in_array($field, $allowed_fields[$table])) {
        $_SESSION['error_message'] = 'Invalid table or field specified.';
        return;
    }
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $params = array_merge([$value], $ids);
        
        $stmt = $db->prepare("UPDATE {$table} SET {$field} = ? WHERE id IN ({$placeholders})");
        $stmt->execute($params);
        
        $updated_count = $stmt->rowCount();
        $_SESSION['success_message'] = "Successfully updated {$updated_count} records in {$table}.";
        
        // Log bulk update
        logAuditEvent($table, 0, 'BULK_UPDATE', null, [
            'field' => $field, 
            'value' => $value, 
            'updated_ids' => $ids, 
            'count' => $updated_count
        ]);
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'Bulk update failed: ' . $e->getMessage();
        error_log('Bulk update error: ' . $e->getMessage());
    }
}

function handleBulkNotifications() {
    $notification_type = $_POST['notification_type'] ?? '';
    $recipients = $_POST['recipients'] ?? [];
    $subject = $_POST['subject'] ?? '';
    $message = $_POST['message'] ?? '';
    
    if (empty($notification_type) || empty($recipients) || empty($subject) || empty($message)) {
        $_SESSION['error_message'] = 'All notification fields are required.';
        return;
    }
    
    // Include email functions
    require_once '../../includes/email.php';
    
    $results = sendBulkEmails($recipients, $subject, $message);
    
    if ($results['success'] > 0) {
        $_SESSION['success_message'] = "Sent {$results['success']} notifications successfully. {$results['failed']} failed.";
    } else {
        $_SESSION['error_message'] = "Failed to send notifications. {$results['failed']} failed.";
    }
}

function importCSVData($file, $table) {
    $success_count = 0;
    $error_count = 0;
    $errors = [];
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        if (($handle = fopen($file, "r")) !== FALSE) {
            $header = fgetcsv($handle); // Skip header row
            $row_number = 1;
            
            while (($data = fgetcsv($handle)) !== FALSE) {
                $row_number++;
                
                try {
                    switch ($table) {
                        case 'suppliers':
                            importSupplierRow($db, $data, $row_number);
                            break;
                        case 'recipients':
                            importRecipientRow($db, $data, $row_number);
                            break;
                        case 'products':
                            importProductRow($db, $data, $row_number);
                            break;
                    }
                    $success_count++;
                } catch (Exception $e) {
                    $error_count++;
                    $errors[] = "Row {$row_number}: " . $e->getMessage();
                }
            }
            fclose($handle);
        }
        
    } catch (Exception $e) {
        $error_count++;
        $errors[] = "File processing error: " . $e->getMessage();
    }
    
    // Log import results
    logAuditEvent($table, 0, 'BULK_IMPORT', null, [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'errors' => $errors
    ]);
    
    return [
        'success' => $success_count,
        'errors' => $error_count,
        'error_details' => $errors
    ];
}

function importSupplierRow($db, $data, $row_number) {
    // Expected CSV format: name, address, state, pincode, gstin, email, phone
    if (count($data) < 7) {
        throw new Exception("Insufficient data columns");
    }
    
    $name = trim($data[0]);
    $address = trim($data[1]);
    $state = trim($data[2]);
    $pincode = trim($data[3]);
    $gstin = trim($data[4]);
    $email = trim($data[5]);
    $phone = trim($data[6]);
    
    // Validate required fields
    if (empty($name) || empty($gstin)) {
        throw new Exception("Name and GSTIN are required");
    }
    
    if (!validateGSTIN($gstin)) {
        throw new Exception("Invalid GSTIN format");
    }
    
    if (!empty($email) && !validateEmail($email)) {
        throw new Exception("Invalid email format");
    }
    
    $stmt = $db->prepare("
        INSERT INTO suppliers (name, address, state, pincode, gstin, email, phone, user_id, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$name, $address, $state, $pincode, $gstin, $email, $phone, $_SESSION['user_id']]);
}

function importRecipientRow($db, $data, $row_number) {
    // Expected CSV format: name, address, state, pincode, gstin, email, phone
    if (count($data) < 7) {
        throw new Exception("Insufficient data columns");
    }
    
    $name = trim($data[0]);
    $address = trim($data[1]);
    $state = trim($data[2]);
    $pincode = trim($data[3]);
    $gstin = trim($data[4]);
    $email = trim($data[5]);
    $phone = trim($data[6]);
    
    // Validate required fields
    if (empty($name) || empty($gstin)) {
        throw new Exception("Name and GSTIN are required");
    }
    
    if (!validateGSTIN($gstin)) {
        throw new Exception("Invalid GSTIN format");
    }
    
    if (!empty($email) && !validateEmail($email)) {
        throw new Exception("Invalid email format");
    }
    
    $stmt = $db->prepare("
        INSERT INTO recipients (name, address, state, pincode, gstin, email, phone, user_id, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$name, $address, $state, $pincode, $gstin, $email, $phone, $_SESSION['user_id']]);
}

function importProductRow($db, $data, $row_number) {
    // Expected CSV format: name, description, hsn_sac_code, unit, rate, tax_rate
    if (count($data) < 6) {
        throw new Exception("Insufficient data columns");
    }
    
    $name = trim($data[0]);
    $description = trim($data[1]);
    $hsn_sac_code = trim($data[2]);
    $unit = trim($data[3]);
    $rate = floatval($data[4]);
    $tax_rate = floatval($data[5]);
    
    // Validate required fields
    if (empty($name) || empty($hsn_sac_code)) {
        throw new Exception("Name and HSN/SAC code are required");
    }
    
    if (!validateHSNSAC($hsn_sac_code)) {
        throw new Exception("Invalid HSN/SAC code format");
    }
    
    if ($rate < 0 || $tax_rate < 0 || $tax_rate > 100) {
        throw new Exception("Invalid rate or tax rate");
    }
    
    $stmt = $db->prepare("
        INSERT INTO products (name, description, hsn_sac_code, unit, rate, tax_rate, user_id, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$name, $description, $hsn_sac_code, $unit, $rate, $tax_rate, $_SESSION['user_id']]);
}

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-tasks me-2"></i>
                Bulk Operations
            </h2>
            <div>
                <a href="../../dashboard.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<!-- CSV Import Section -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Import Suppliers
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="operation" value="import_suppliers">

                    <div class="mb-3">
                        <label for="supplier_csv" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="supplier_csv" name="csv_file" accept=".csv" required>
                        <div class="form-text">Format: name, address, state, pincode, gstin, email, phone</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>
                        Import Suppliers
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Import Recipients
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="operation" value="import_recipients">

                    <div class="mb-3">
                        <label for="recipient_csv" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="recipient_csv" name="csv_file" accept=".csv" required>
                        <div class="form-text">Format: name, address, state, pincode, gstin, email, phone</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>
                        Import Recipients
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Import Products
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="operation" value="import_products">

                    <div class="mb-3">
                        <label for="product_csv" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="product_csv" name="csv_file" accept=".csv" required>
                        <div class="form-text">Format: name, description, hsn_sac_code, unit, rate, tax_rate</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>
                        Import Products
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Operations Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Bulk Update
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="bulkUpdateForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="operation" value="bulk_update">

                    <div class="mb-3">
                        <label for="update_table" class="form-label">Table</label>
                        <select class="form-select" id="update_table" name="table" required>
                            <option value="">Select Table</option>
                            <option value="suppliers">Suppliers</option>
                            <option value="recipients">Recipients</option>
                            <option value="products">Products</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="update_field" class="form-label">Field</label>
                        <select class="form-select" id="update_field" name="field" required>
                            <option value="">Select Field</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="update_value" class="form-label">New Value</label>
                        <input type="text" class="form-control" id="update_value" name="value" required>
                    </div>

                    <div class="mb-3">
                        <label for="update_ids" class="form-label">Record IDs (comma-separated)</label>
                        <textarea class="form-control" id="update_ids" name="ids_text" rows="3" placeholder="1,2,3,4,5" required></textarea>
                    </div>

                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>
                        Bulk Update
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trash me-2"></i>
                    Bulk Delete
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="bulkDeleteForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="operation" value="bulk_delete">

                    <div class="mb-3">
                        <label for="delete_table" class="form-label">Table</label>
                        <select class="form-select" id="delete_table" name="table" required>
                            <option value="">Select Table</option>
                            <option value="suppliers">Suppliers</option>
                            <option value="recipients">Recipients</option>
                            <option value="products">Products</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="delete_ids" class="form-label">Record IDs (comma-separated)</label>
                        <textarea class="form-control" id="delete_ids" name="ids_text" rows="3" placeholder="1,2,3,4,5" required></textarea>
                    </div>

                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete these records? This action cannot be undone.')">
                        <i class="fas fa-trash me-2"></i>
                        Bulk Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Notifications Section -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    Bulk Notifications
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="operation" value="send_notifications">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notification_type" class="form-label">Notification Type</label>
                                <select class="form-select" id="notification_type" name="notification_type" required>
                                    <option value="">Select Type</option>
                                    <option value="general">General Announcement</option>
                                    <option value="reminder">Payment Reminder</option>
                                    <option value="update">System Update</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="recipients_list" class="form-label">Recipients (one email per line)</label>
                                <textarea class="form-control" id="recipients_list" name="recipients_text" rows="5" placeholder="<EMAIL>&#10;<EMAIL>" required></textarea>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notification_subject" class="form-label">Subject</label>
                                <input type="text" class="form-control" id="notification_subject" name="subject" required>
                            </div>

                            <div class="mb-3">
                                <label for="notification_message" class="form-label">Message</label>
                                <textarea class="form-control" id="notification_message" name="message" rows="5" required></textarea>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-envelope me-2"></i>
                        Send Bulk Notifications
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Handle field options based on table selection
document.getElementById('update_table').addEventListener('change', function() {
    const table = this.value;
    const fieldSelect = document.getElementById('update_field');

    fieldSelect.innerHTML = '<option value="">Select Field</option>';

    const fieldOptions = {
        'suppliers': [
            {value: 'state', text: 'State'},
            {value: 'status', text: 'Status'}
        ],
        'recipients': [
            {value: 'state', text: 'State'},
            {value: 'status', text: 'Status'}
        ],
        'products': [
            {value: 'tax_rate', text: 'Tax Rate'},
            {value: 'unit', text: 'Unit'},
            {value: 'status', text: 'Status'}
        ]
    };

    if (fieldOptions[table]) {
        fieldOptions[table].forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            fieldSelect.appendChild(optionElement);
        });
    }
});

// Convert comma-separated IDs to array before form submission
document.getElementById('bulkUpdateForm').addEventListener('submit', function(e) {
    const idsText = document.getElementById('update_ids').value;
    const idsArray = idsText.split(',').map(id => id.trim()).filter(id => id);

    // Create hidden inputs for each ID
    idsArray.forEach(id => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'ids[]';
        hiddenInput.value = id;
        this.appendChild(hiddenInput);
    });
});

document.getElementById('bulkDeleteForm').addEventListener('submit', function(e) {
    const idsText = document.getElementById('delete_ids').value;
    const idsArray = idsText.split(',').map(id => id.trim()).filter(id => id);

    // Create hidden inputs for each ID
    idsArray.forEach(id => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'ids[]';
        hiddenInput.value = id;
        this.appendChild(hiddenInput);
    });
});

// Convert recipients text to array
document.querySelector('form[method="POST"]:last-of-type').addEventListener('submit', function(e) {
    const recipientsText = document.getElementById('recipients_list').value;
    const recipientsArray = recipientsText.split('\n').map(email => email.trim()).filter(email => email);

    // Create hidden inputs for each recipient
    recipientsArray.forEach(email => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'recipients[]';
        hiddenInput.value = email;
        this.appendChild(hiddenInput);
    });
});
</script>

<?php include '../../includes/footer.php'; ?>
