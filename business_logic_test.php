<?php
/**
 * Business Logic Testing
 * GST e-Invoice Application
 * 
 * Tests real-time calculations, constraints, and business rules
 */

require_once 'config/config.php';

// Test invoice creation with real-time calculations
function testInvoiceCalculations() {
    echo "<h3>Testing Invoice Calculations</h3>\n";
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Test data
        $test_items = [
            [
                'product_name' => 'Test Product 1',
                'quantity' => 2,
                'rate' => 1000,
                'tax_rate' => 18,
                'amount' => 2000
            ],
            [
                'product_name' => 'Test Product 2',
                'quantity' => 1,
                'rate' => 500,
                'tax_rate' => 12,
                'amount' => 500
            ]
        ];
        
        $supplier_state = 'Maharashtra';
        $recipient_state = 'Maharashtra'; // Same state for CGST+SGST
        
        $subtotal = 0;
        $total_cgst = 0;
        $total_sgst = 0;
        $total_igst = 0;
        
        echo "<div class='alert alert-info'>Testing same-state transaction (CGST + SGST)</div>\n";
        
        foreach ($test_items as $item) {
            $subtotal += $item['amount'];
            
            if ($supplier_state === $recipient_state) {
                // Same state - CGST + SGST
                $tax_amount = ($item['amount'] * $item['tax_rate']) / 100;
                $cgst = $tax_amount / 2;
                $sgst = $tax_amount / 2;
                $igst = 0;
            } else {
                // Different states - IGST
                $cgst = 0;
                $sgst = 0;
                $igst = ($item['amount'] * $item['tax_rate']) / 100;
            }
            
            $total_cgst += $cgst;
            $total_sgst += $sgst;
            $total_igst += $igst;
            
            echo "<p>Item: {$item['product_name']} - Amount: ₹{$item['amount']}, Tax: {$item['tax_rate']}%, CGST: ₹{$cgst}, SGST: ₹{$sgst}</p>\n";
        }
        
        $total_amount = $subtotal + $total_cgst + $total_sgst + $total_igst;
        
        echo "<div class='card mt-3'>\n";
        echo "<div class='card-body'>\n";
        echo "<h5>Calculation Summary</h5>\n";
        echo "<p>Subtotal: ₹" . number_format($subtotal, 2) . "</p>\n";
        echo "<p>Total CGST: ₹" . number_format($total_cgst, 2) . "</p>\n";
        echo "<p>Total SGST: ₹" . number_format($total_sgst, 2) . "</p>\n";
        echo "<p>Total IGST: ₹" . number_format($total_igst, 2) . "</p>\n";
        echo "<p><strong>Total Amount: ₹" . number_format($total_amount, 2) . "</strong></p>\n";
        echo "</div>\n";
        echo "</div>\n";
        
        // Test different state calculation
        echo "<div class='alert alert-warning mt-3'>Testing different-state transaction (IGST)</div>\n";
        
        $recipient_state = 'Karnataka'; // Different state
        $total_igst_diff = 0;
        
        foreach ($test_items as $item) {
            $igst = ($item['amount'] * $item['tax_rate']) / 100;
            $total_igst_diff += $igst;
            echo "<p>Item: {$item['product_name']} - Amount: ₹{$item['amount']}, Tax: {$item['tax_rate']}%, IGST: ₹{$igst}</p>\n";
        }
        
        $total_amount_diff = $subtotal + $total_igst_diff;
        
        echo "<div class='card mt-3'>\n";
        echo "<div class='card-body'>\n";
        echo "<h5>Different State Calculation Summary</h5>\n";
        echo "<p>Subtotal: ₹" . number_format($subtotal, 2) . "</p>\n";
        echo "<p>Total IGST: ₹" . number_format($total_igst_diff, 2) . "</p>\n";
        echo "<p><strong>Total Amount: ₹" . number_format($total_amount_diff, 2) . "</strong></p>\n";
        echo "</div>\n";
        echo "</div>\n";
        
        return true;
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>\n";
        return false;
    }
}

// Test GSTIN validation with real examples
function testGSTINValidation() {
    echo "<h3>Testing GSTIN Validation</h3>\n";
    
    $test_gstins = [
        '27AAPFU0939F1ZV' => true,  // Valid
        '33AAPFU0939F1ZV' => true,  // Valid
        '07AAPFU0939F1ZV' => true,  // Valid
        '27AAPFU0939F1Z' => false,  // Too short
        '27AAPFU0939F1ZX' => false, // Invalid check digit
        'INVALID_GSTIN' => false,   // Invalid format
        '' => false,                // Empty
        '123456789012345' => false  // All numbers
    ];
    
    echo "<table class='table table-striped'>\n";
    echo "<thead><tr><th>GSTIN</th><th>Expected</th><th>Result</th><th>Status</th></tr></thead>\n";
    echo "<tbody>\n";
    
    $passed = 0;
    $total = count($test_gstins);
    
    foreach ($test_gstins as $gstin => $expected) {
        $result = validateGSTIN($gstin);
        $status = ($result === $expected) ? 'PASS' : 'FAIL';
        $status_class = ($result === $expected) ? 'success' : 'danger';
        
        if ($result === $expected) $passed++;
        
        echo "<tr>\n";
        echo "<td><code>" . htmlspecialchars($gstin) . "</code></td>\n";
        echo "<td>" . ($expected ? 'Valid' : 'Invalid') . "</td>\n";
        echo "<td>" . ($result ? 'Valid' : 'Invalid') . "</td>\n";
        echo "<td><span class='badge bg-{$status_class}'>{$status}</span></td>\n";
        echo "</tr>\n";
    }
    
    echo "</tbody>\n";
    echo "</table>\n";
    
    echo "<div class='alert alert-info'>GSTIN Validation: {$passed}/{$total} tests passed</div>\n";
    
    return $passed === $total;
}

// Test HSN/SAC code validation
function testHSNSACValidation() {
    echo "<h3>Testing HSN/SAC Code Validation</h3>\n";
    
    $test_codes = [
        '1234' => true,     // Valid 4-digit HSN
        '123456' => true,   // Valid 6-digit HSN
        '12345678' => true, // Valid 8-digit HSN
        '998361' => true,   // Valid SAC code
        '123' => false,     // Too short
        '123456789' => false, // Too long
        '12AB56' => false,  // Contains letters
        '' => false         // Empty
    ];
    
    echo "<table class='table table-striped'>\n";
    echo "<thead><tr><th>HSN/SAC Code</th><th>Expected</th><th>Result</th><th>Status</th></tr></thead>\n";
    echo "<tbody>\n";
    
    $passed = 0;
    $total = count($test_codes);
    
    foreach ($test_codes as $code => $expected) {
        $result = validateHSNSAC($code);
        $status = ($result === $expected) ? 'PASS' : 'FAIL';
        $status_class = ($result === $expected) ? 'success' : 'danger';
        
        if ($result === $expected) $passed++;
        
        echo "<tr>\n";
        echo "<td><code>" . htmlspecialchars($code) . "</code></td>\n";
        echo "<td>" . ($expected ? 'Valid' : 'Invalid') . "</td>\n";
        echo "<td>" . ($result ? 'Valid' : 'Invalid') . "</td>\n";
        echo "<td><span class='badge bg-{$status_class}'>{$status}</span></td>\n";
        echo "</tr>\n";
    }
    
    echo "</tbody>\n";
    echo "</table>\n";
    
    echo "<div class='alert alert-info'>HSN/SAC Validation: {$passed}/{$total} tests passed</div>\n";
    
    return $passed === $total;
}

// Test invoice number generation uniqueness
function testInvoiceNumberGeneration() {
    echo "<h3>Testing Invoice Number Generation</h3>\n";
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Generate multiple invoice numbers and check uniqueness
        $generated_numbers = [];
        $duplicates = 0;
        
        for ($i = 0; $i < 10; $i++) {
            $invoice_number = generateInvoiceNumber();
            
            if (in_array($invoice_number, $generated_numbers)) {
                $duplicates++;
            } else {
                $generated_numbers[] = $invoice_number;
            }
            
            echo "<p>Generated: <code>{$invoice_number}</code></p>\n";
        }
        
        if ($duplicates === 0) {
            echo "<div class='alert alert-success'>All generated invoice numbers are unique</div>\n";
            return true;
        } else {
            echo "<div class='alert alert-warning'>Found {$duplicates} duplicate invoice numbers</div>\n";
            return false;
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>\n";
        return false;
    }
}

// Test tax rate constraints
function testTaxRateConstraints() {
    echo "<h3>Testing Tax Rate Constraints</h3>\n";
    
    $test_rates = [
        0 => true,      // Valid - 0%
        5 => true,      // Valid - 5%
        12 => true,     // Valid - 12%
        18 => true,     // Valid - 18%
        28 => true,     // Valid - 28%
        -5 => false,    // Invalid - negative
        150 => false,   // Invalid - over 100%
        'abc' => false  // Invalid - non-numeric
    ];
    
    echo "<table class='table table-striped'>\n";
    echo "<thead><tr><th>Tax Rate</th><th>Expected</th><th>Result</th><th>Status</th></tr></thead>\n";
    echo "<tbody>\n";
    
    $passed = 0;
    $total = count($test_rates);
    
    foreach ($test_rates as $rate => $expected) {
        $result = validateTaxRate($rate);
        $status = ($result === $expected) ? 'PASS' : 'FAIL';
        $status_class = ($result === $expected) ? 'success' : 'danger';
        
        if ($result === $expected) $passed++;
        
        echo "<tr>\n";
        echo "<td>" . htmlspecialchars($rate) . "%</td>\n";
        echo "<td>" . ($expected ? 'Valid' : 'Invalid') . "</td>\n";
        echo "<td>" . ($result ? 'Valid' : 'Invalid') . "</td>\n";
        echo "<td><span class='badge bg-{$status_class}'>{$status}</span></td>\n";
        echo "</tr>\n";
    }
    
    echo "</tbody>\n";
    echo "</table>\n";
    
    echo "<div class='alert alert-info'>Tax Rate Validation: {$passed}/{$total} tests passed</div>\n";
    
    return $passed === $total;
}

// Helper function for tax rate validation
function validateTaxRate($rate) {
    return is_numeric($rate) && $rate >= 0 && $rate <= 100;
}

// Helper function for invoice number generation
function generateInvoiceNumber() {
    return 'INV-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

// Test database constraints and relationships
function testDatabaseConstraints() {
    echo "<h3>Testing Database Constraints</h3>\n";
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Test foreign key constraints
        echo "<h5>Testing Foreign Key Constraints</h5>\n";
        
        // Check if invoices table has proper foreign keys
        $stmt = $db->query("
            SELECT 
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'invoices' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $foreign_keys = $stmt->fetchAll();
        
        if (count($foreign_keys) >= 3) { // Should have at least supplier_id, recipient_id, user_id
            echo "<div class='alert alert-success'>Foreign key constraints are properly defined</div>\n";
        } else {
            echo "<div class='alert alert-warning'>Some foreign key constraints may be missing</div>\n";
        }
        
        foreach ($foreign_keys as $fk) {
            echo "<p>Foreign Key: {$fk['COLUMN_NAME']} → {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</p>\n";
        }
        
        // Test unique constraints
        echo "<h5>Testing Unique Constraints</h5>\n";
        
        $stmt = $db->query("SHOW INDEX FROM users WHERE Key_name != 'PRIMARY'");
        $indexes = $stmt->fetchAll();
        
        $has_email_unique = false;
        foreach ($indexes as $index) {
            if ($index['Column_name'] === 'email' && $index['Non_unique'] == 0) {
                $has_email_unique = true;
                break;
            }
        }
        
        if ($has_email_unique) {
            echo "<div class='alert alert-success'>Email unique constraint is properly defined</div>\n";
        } else {
            echo "<div class='alert alert-warning'>Email unique constraint may be missing</div>\n";
        }
        
        return true;
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>\n";
        return false;
    }
}

// Run all tests
$tests = [
    'Invoice Calculations' => 'testInvoiceCalculations',
    'GSTIN Validation' => 'testGSTINValidation',
    'HSN/SAC Validation' => 'testHSNSACValidation',
    'Invoice Number Generation' => 'testInvoiceNumberGeneration',
    'Tax Rate Constraints' => 'testTaxRateConstraints',
    'Database Constraints' => 'testDatabaseConstraints'
];

$passed_tests = 0;
$total_tests = count($tests);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Logic Testing - GST e-Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Business Logic Testing</h1>
        <p class="lead">Testing real-time calculations, constraints, and business rules</p>
        
        <div class="row">
            <?php foreach ($tests as $test_name => $test_function): ?>
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h4><?php echo $test_name; ?></h4>
                        </div>
                        <div class="card-body">
                            <?php 
                            $result = $test_function();
                            if ($result) $passed_tests++;
                            ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="alert alert-<?php echo ($passed_tests === $total_tests) ? 'success' : 'warning'; ?>">
            <h4>Test Summary</h4>
            <p>Passed: <?php echo $passed_tests; ?>/<?php echo $total_tests; ?> tests</p>
            <p>Success Rate: <?php echo round(($passed_tests / $total_tests) * 100, 1); ?>%</p>
        </div>
        
        <div class="mt-4">
            <a href="dashboard.php" class="btn btn-primary">Back to Dashboard</a>
            <a href="data_validation_test.php" class="btn btn-secondary">Run Validation Tests</a>
            <a href="comprehensive_test.php" class="btn btn-info">Run Full System Test</a>
        </div>
    </div>
</body>
</html>
