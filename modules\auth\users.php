<?php
/**
 * User Management Page (Admin Only)
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require admin access
requireAdmin();

$page_title = 'User Management';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        $csrf_token = $_POST['csrf_token'] ?? '';
        
        if (!validateCSRFToken($csrf_token)) {
            $_SESSION['error_message'] = 'Invalid request. Please try again.';
        } else {
            switch ($action) {
                case 'create':
                    $name = sanitizeInput($_POST['name'] ?? '');
                    $email = sanitizeInput($_POST['email'] ?? '');
                    $password = $_POST['password'] ?? '';
                    $role = $_POST['role'] ?? 'standard';
                    
                    if (empty($name) || empty($email) || empty($password)) {
                        $_SESSION['error_message'] = 'All fields are required.';
                    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $_SESSION['error_message'] = 'Invalid email address.';
                    } elseif (strlen($password) < 6) {
                        $_SESSION['error_message'] = 'Password must be at least 6 characters.';
                    } else {
                        // Check if email already exists
                        $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
                        $stmt->execute([$email]);
                        
                        if ($stmt->fetch()) {
                            $_SESSION['error_message'] = 'Email address already exists.';
                        } else {
                            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                            
                            $stmt = $db->prepare("
                                INSERT INTO users (name, email, password, role) 
                                VALUES (?, ?, ?, ?)
                            ");
                            
                            if ($stmt->execute([$name, $email, $hashed_password, $role])) {
                                $_SESSION['success_message'] = 'User created successfully.';
                                
                                // Log user creation
                                $user_id = $db->lastInsertId();
                                $stmt = $db->prepare("
                                    INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                    VALUES (?, 'users', ?, 'INSERT', ?, ?, ?)
                                ");
                                $stmt->execute([
                                    $_SESSION['user_id'], 
                                    $user_id, 
                                    json_encode(['name' => $name, 'email' => $email, 'role' => $role]),
                                    $_SERVER['REMOTE_ADDR'] ?? '',
                                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                                ]);
                            } else {
                                $_SESSION['error_message'] = 'Failed to create user.';
                            }
                        }
                    }
                    break;
                    
                case 'update':
                    $user_id = (int)($_POST['user_id'] ?? 0);
                    $name = sanitizeInput($_POST['name'] ?? '');
                    $email = sanitizeInput($_POST['email'] ?? '');
                    $role = $_POST['role'] ?? 'standard';
                    $is_active = isset($_POST['is_active']) ? 1 : 0;
                    
                    if (empty($name) || empty($email)) {
                        $_SESSION['error_message'] = 'Name and email are required.';
                    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $_SESSION['error_message'] = 'Invalid email address.';
                    } elseif ($user_id == $_SESSION['user_id'] && !$is_active) {
                        $_SESSION['error_message'] = 'You cannot deactivate your own account.';
                    } else {
                        // Check if email exists for other users
                        $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                        $stmt->execute([$email, $user_id]);
                        
                        if ($stmt->fetch()) {
                            $_SESSION['error_message'] = 'Email address already exists.';
                        } else {
                            $stmt = $db->prepare("
                                UPDATE users 
                                SET name = ?, email = ?, role = ?, is_active = ? 
                                WHERE id = ?
                            ");
                            
                            if ($stmt->execute([$name, $email, $role, $is_active, $user_id])) {
                                $_SESSION['success_message'] = 'User updated successfully.';
                                
                                // Log user update
                                $stmt = $db->prepare("
                                    INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                    VALUES (?, 'users', ?, 'UPDATE', ?, ?, ?)
                                ");
                                $stmt->execute([
                                    $_SESSION['user_id'], 
                                    $user_id, 
                                    json_encode(['name' => $name, 'email' => $email, 'role' => $role, 'is_active' => $is_active]),
                                    $_SERVER['REMOTE_ADDR'] ?? '',
                                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                                ]);
                            } else {
                                $_SESSION['error_message'] = 'Failed to update user.';
                            }
                        }
                    }
                    break;
                    
                case 'delete':
                    $user_id = (int)($_POST['user_id'] ?? 0);
                    
                    if ($user_id == $_SESSION['user_id']) {
                        $_SESSION['error_message'] = 'You cannot delete your own account.';
                    } else {
                        // Check if user has invoices
                        $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoices WHERE user_id = ?");
                        $stmt->execute([$user_id]);
                        $invoice_count = $stmt->fetch()['count'];
                        
                        if ($invoice_count > 0) {
                            $_SESSION['error_message'] = 'Cannot delete user with existing invoices. Deactivate instead.';
                        } else {
                            $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
                            
                            if ($stmt->execute([$user_id])) {
                                $_SESSION['success_message'] = 'User deleted successfully.';
                                
                                // Log user deletion
                                $stmt = $db->prepare("
                                    INSERT INTO audit_log (user_id, table_name, record_id, action, old_values, ip_address, user_agent) 
                                    VALUES (?, 'users', ?, 'DELETE', ?, ?, ?)
                                ");
                                $stmt->execute([
                                    $_SESSION['user_id'], 
                                    $user_id, 
                                    json_encode(['deleted_by' => $_SESSION['user_name']]),
                                    $_SERVER['REMOTE_ADDR'] ?? '',
                                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                                ]);
                            } else {
                                $_SESSION['error_message'] = 'Failed to delete user.';
                            }
                        }
                    }
                    break;
                    
                case 'reset_password':
                    $user_id = (int)($_POST['user_id'] ?? 0);
                    $new_password = $_POST['new_password'] ?? '';
                    
                    if (strlen($new_password) < 6) {
                        $_SESSION['error_message'] = 'Password must be at least 6 characters.';
                    } else {
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        
                        $stmt = $db->prepare("
                            UPDATE users 
                            SET password = ?, failed_attempts = 0, locked_until = NULL 
                            WHERE id = ?
                        ");
                        
                        if ($stmt->execute([$hashed_password, $user_id])) {
                            $_SESSION['success_message'] = 'Password reset successfully.';
                            
                            // Log password reset
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, new_values, ip_address, user_agent) 
                                VALUES (?, 'users', ?, 'PASSWORD_RESET_ADMIN', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $user_id, 
                                json_encode(['reset_by' => $_SESSION['user_name']]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        } else {
                            $_SESSION['error_message'] = 'Failed to reset password.';
                        }
                    }
                    break;
            }
        }
        
        header('Location: users.php');
        exit();
    }
    
    // Get all users
    $stmt = $db->prepare("
        SELECT u.*, 
               COUNT(i.id) as invoice_count,
               MAX(i.created_at) as last_invoice_date
        FROM users u
        LEFT JOIN invoices i ON u.id = i.user_id
        GROUP BY u.id
        ORDER BY u.created_at DESC
    ");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log('User management error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while loading users.';
    $users = [];
}

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-users me-2"></i>
                User Management
            </h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                <i class="fas fa-plus me-2"></i>
                Add User
            </button>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Invoices</th>
                                <th>Last Login</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                        <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                            <span class="badge bg-info ms-1">You</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : 'primary'; ?>">
                                            <?php echo ucfirst($user['role']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($user['is_active']): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                        
                                        <?php if ($user['locked_until'] && strtotime($user['locked_until']) > time()): ?>
                                            <span class="badge bg-warning">Locked</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['invoice_count'] > 0): ?>
                                            <span class="badge bg-info"><?php echo $user['invoice_count']; ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">None</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['last_login']): ?>
                                            <?php echo formatDateTime($user['last_login']); ?>
                                        <?php else: ?>
                                            <span class="text-muted">Never</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo formatDateTime($user['created_at']); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="editUser(<?php echo htmlspecialchars(json_encode($user)); ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="resetPassword(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['name']); ?>')">
                                                <i class="fas fa-key"></i>
                                            </button>
                                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['name']); ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    Add New User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="create">

                <div class="modal-body">
                    <div class="mb-3">
                        <label for="create_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="create_name" name="name" required>
                        <div class="invalid-feedback">Please enter the user's name.</div>
                    </div>

                    <div class="mb-3">
                        <label for="create_email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="create_email" name="email" required>
                        <div class="invalid-feedback">Please enter a valid email address.</div>
                    </div>

                    <div class="mb-3">
                        <label for="create_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="create_password" name="password" minlength="6" required>
                        <div class="invalid-feedback">Password must be at least 6 characters.</div>
                    </div>

                    <div class="mb-3">
                        <label for="create_role" class="form-label">Role</label>
                        <select class="form-select" id="create_role" name="role" required>
                            <option value="standard">Standard User</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>
                    Edit User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="user_id" id="edit_user_id">

                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                        <div class="invalid-feedback">Please enter the user's name.</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                        <div class="invalid-feedback">Please enter a valid email address.</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="standard">Standard User</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">
                                Active User
                            </label>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>
                    Reset Password
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="reset_password">
                <input type="hidden" name="user_id" id="reset_user_id">

                <div class="modal-body">
                    <p>Reset password for: <strong id="reset_user_name"></strong></p>

                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" minlength="6" required>
                        <div class="invalid-feedback">Password must be at least 6 characters.</div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key me-2"></i>
                        Reset Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-trash me-2"></i>
                    Delete User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="user_id" id="delete_user_id">

                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Are you sure you want to delete this user?
                    </div>
                    <p>User: <strong id="delete_user_name"></strong></p>
                    <p class="text-muted">This action cannot be undone. Users with existing invoices cannot be deleted.</p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        Delete User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editUser(user) {
    document.getElementById('edit_user_id').value = user.id;
    document.getElementById('edit_name').value = user.name;
    document.getElementById('edit_email').value = user.email;
    document.getElementById('edit_role').value = user.role;
    document.getElementById('edit_is_active').checked = user.is_active == 1;

    new bootstrap.Modal(document.getElementById('editUserModal')).show();
}

function resetPassword(userId, userName) {
    document.getElementById('reset_user_id').value = userId;
    document.getElementById('reset_user_name').textContent = userName;
    document.getElementById('new_password').value = '';

    new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
}

function deleteUser(userId, userName) {
    document.getElementById('delete_user_id').value = userId;
    document.getElementById('delete_user_name').textContent = userName;

    new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
}
</script>

<?php include '../../includes/footer.php'; ?>
