<?php
/**
 * Invoice List and Management
 * GST e-Invoice Application
 */

require_once '../../config/config.php';

// Require login
requireLogin();

$page_title = 'Invoice Management';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle bulk actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
        $csrf_token = $_POST['csrf_token'] ?? '';
        $action = $_POST['bulk_action'] ?? '';
        $selected_invoices = $_POST['selected_invoices'] ?? [];
        
        if (!validateCSRFToken($csrf_token)) {
            $_SESSION['error_message'] = 'Invalid request. Please try again.';
        } elseif (empty($selected_invoices)) {
            $_SESSION['error_message'] = 'Please select at least one invoice.';
        } else {
            switch ($action) {
                case 'delete':
                    $deleted_count = 0;
                    foreach ($selected_invoices as $invoice_id) {
                        $invoice_id = (int)$invoice_id;
                        
                        // Check if user can delete this invoice
                        if (!isAdmin()) {
                            $stmt = $db->prepare("SELECT user_id FROM invoices WHERE id = ?");
                            $stmt->execute([$invoice_id]);
                            $invoice_user = $stmt->fetch();
                            
                            if (!$invoice_user || $invoice_user['user_id'] != $_SESSION['user_id']) {
                                continue;
                            }
                        }
                        
                        // Delete invoice items first
                        $stmt = $db->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
                        $stmt->execute([$invoice_id]);
                        
                        // Delete invoice
                        $stmt = $db->prepare("DELETE FROM invoices WHERE id = ?");
                        if ($stmt->execute([$invoice_id])) {
                            $deleted_count++;
                            
                            // Log deletion
                            $stmt = $db->prepare("
                                INSERT INTO audit_log (user_id, table_name, record_id, action, old_values, ip_address, user_agent) 
                                VALUES (?, 'invoices', ?, 'DELETE', ?, ?, ?)
                            ");
                            $stmt->execute([
                                $_SESSION['user_id'], 
                                $invoice_id, 
                                json_encode(['deleted_by' => $_SESSION['user_name']]),
                                $_SERVER['REMOTE_ADDR'] ?? '',
                                $_SERVER['HTTP_USER_AGENT'] ?? ''
                            ]);
                        }
                    }
                    
                    if ($deleted_count > 0) {
                        $_SESSION['success_message'] = "$deleted_count invoice(s) deleted successfully.";
                    }
                    break;
                    
                case 'export_json':
                    // Generate JSON for selected invoices
                    $json_files = [];
                    foreach ($selected_invoices as $invoice_id) {
                        $invoice_id = (int)$invoice_id;
                        
                        // Check access
                        if (!isAdmin()) {
                            $stmt = $db->prepare("SELECT user_id FROM invoices WHERE id = ?");
                            $stmt->execute([$invoice_id]);
                            $invoice_user = $stmt->fetch();
                            
                            if (!$invoice_user || $invoice_user['user_id'] != $_SESSION['user_id']) {
                                continue;
                            }
                        }
                        
                        $json_files[] = "generate_json.php?id=$invoice_id";
                    }
                    
                    if (!empty($json_files)) {
                        $_SESSION['json_files'] = $json_files;
                        $_SESSION['success_message'] = count($json_files) . " JSON file(s) ready for download.";
                    }
                    break;
            }
        }
        
        header('Location: list.php');
        exit();
    }
    
    // Get filter parameters
    $search = $_GET['search'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    $supplier_filter = $_GET['supplier'] ?? '';
    $recipient_filter = $_GET['recipient'] ?? '';
    $page = max(1, (int)($_GET['page'] ?? 1));
    $per_page = 25;
    $offset = ($page - 1) * $per_page;
    
    // Build WHERE conditions
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(i.invoice_number LIKE ? OR s.name LIKE ? OR r.name LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "i.status = ?";
        $params[] = $status_filter;
    }
    
    if (!empty($date_from)) {
        $where_conditions[] = "i.date >= ?";
        $params[] = $date_from;
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "i.date <= ?";
        $params[] = $date_to;
    }
    
    if (!empty($supplier_filter)) {
        $where_conditions[] = "i.supplier_id = ?";
        $params[] = $supplier_filter;
    }
    
    if (!empty($recipient_filter)) {
        $where_conditions[] = "i.recipient_id = ?";
        $params[] = $recipient_filter;
    }
    
    // Add user restriction for non-admin users
    if (!isAdmin()) {
        $where_conditions[] = "i.user_id = ?";
        $params[] = $_SESSION['user_id'];
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count for pagination
    $count_stmt = $db->prepare("
        SELECT COUNT(*) as total
        FROM invoices i
        JOIN suppliers s ON i.supplier_id = s.id
        JOIN recipients r ON i.recipient_id = r.id
        $where_clause
    ");
    $count_stmt->execute($params);
    $total_records = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_records / $per_page);
    
    // Get invoices with pagination
    $stmt = $db->prepare("
        SELECT i.*, 
               s.name as supplier_name, s.gstin as supplier_gstin,
               r.name as recipient_name, r.gstin as recipient_gstin,
               u.name as created_by_name
        FROM invoices i
        JOIN suppliers s ON i.supplier_id = s.id
        JOIN recipients r ON i.recipient_id = r.id
        JOIN users u ON i.user_id = u.id
        $where_clause
        ORDER BY i.created_at DESC
        LIMIT $per_page OFFSET $offset
    ");
    $stmt->execute($params);
    $invoices = $stmt->fetchAll();
    
    // Get suppliers for filter dropdown
    if (isAdmin()) {
        $stmt = $db->prepare("SELECT DISTINCT s.id, s.name FROM suppliers s JOIN invoices i ON s.id = i.supplier_id ORDER BY s.name");
        $stmt->execute();
    } else {
        $stmt = $db->prepare("SELECT DISTINCT s.id, s.name FROM suppliers s JOIN invoices i ON s.id = i.supplier_id WHERE i.user_id = ? ORDER BY s.name");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $suppliers = $stmt->fetchAll();
    
    // Get recipients for filter dropdown
    if (isAdmin()) {
        $stmt = $db->prepare("SELECT DISTINCT r.id, r.name FROM recipients r JOIN invoices i ON r.id = i.recipient_id ORDER BY r.name");
        $stmt->execute();
    } else {
        $stmt = $db->prepare("SELECT DISTINCT r.id, r.name FROM recipients r JOIN invoices i ON r.id = i.recipient_id WHERE i.user_id = ? ORDER BY r.name");
        $stmt->execute([$_SESSION['user_id']]);
    }
    $recipients = $stmt->fetchAll();
    
    // Calculate summary statistics
    $stats_params = $params;
    if (!isAdmin()) {
        // Remove the user restriction from params for stats calculation
        array_pop($stats_params);
        $stats_where = str_replace(' AND i.user_id = ?', '', $where_clause);
        if (!isAdmin()) {
            $stats_where .= empty($where_conditions) ? 'WHERE i.user_id = ?' : ' AND i.user_id = ?';
            $stats_params[] = $_SESSION['user_id'];
        }
    } else {
        $stats_where = $where_clause;
    }
    
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_invoices,
            SUM(i.total_amount) as total_amount,
            SUM(CASE WHEN i.status = 'draft' THEN 1 ELSE 0 END) as draft_count,
            SUM(CASE WHEN i.status = 'sent' THEN 1 ELSE 0 END) as sent_count,
            SUM(CASE WHEN i.status = 'paid' THEN 1 ELSE 0 END) as paid_count
        FROM invoices i
        JOIN suppliers s ON i.supplier_id = s.id
        JOIN recipients r ON i.recipient_id = r.id
        $stats_where
    ");
    $stmt->execute($stats_params);
    $stats = $stmt->fetch();
    
} catch (Exception $e) {
    error_log('Invoice list error: ' . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while loading invoices.';
    $invoices = [];
    $suppliers = [];
    $recipients = [];
    $stats = ['total_invoices' => 0, 'total_amount' => 0, 'draft_count' => 0, 'sent_count' => 0, 'paid_count' => 0];
    $total_pages = 0;
}

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-file-invoice me-2"></i>
                Invoice Management
            </h2>
            <div>
                <a href="create.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create Invoice
                </a>
                <a href="../reports/invoices.php" class="btn btn-outline-info">
                    <i class="fas fa-chart-bar me-2"></i>
                    Reports
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Invoices</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['total_invoices']); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Amount</h6>
                        <h3 class="mb-0">₹<?php echo number_format($stats['total_amount'], 2); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-rupee-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Draft</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['draft_count']); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-edit fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Paid</h6>
                        <h3 class="mb-0"><?php echo number_format($stats['paid_count']); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
